import { fileURLToPath, URL } from 'node:url'

import { loadEnv, ConfigEnv, UserConfig } from 'vite'
import { wrapperEnv } from './build/utils'
import { createVitePlugins } from './build/plugins';

import autoprefixer from 'autoprefixer'
import viewport from 'postcss-mobile-forever'

// https://vitejs.dev/config/
export default ({ command, mode }: ConfigEnv): UserConfig => {

  const env = loadEnv(mode, process.cwd())

  const viteEnv = wrapperEnv(env)

  const isBuild = command === 'build';


  return {
    plugins: [
      ...createVitePlugins(viteEnv, isBuild),
      {
        name: 'generate-config-plugin',
        closeBundle() {
          const { runBuildConfig } = require('./build/script/buildConf.js');
          runBuildConfig(mode); // 传递当前模式
        },
      },
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      }
    },
    server: {
      host: true,
      port: 7779,
    },
    css: {
      modules: {
        localsConvention: 'camelCaseOnly'
      },
      postcss: {
        plugins: [
          autoprefixer(),
          viewport({
            appSelector: '#app',
            viewportWidth: 375,
            maxDisplayWidth: 600,
            appContainingBlock: 'manual',
            enableMediaQuery: true,
            disableLandscape: true,
            rootContainingBlockSelectorList: [
              'van-popup',
              'van-tabbar'
            ]
          }),
        ]
      }
    },
    esbuild: {
      jsxFactory: 'h',
      jsxFragment: 'Fragment',
    },
    build: {
      target: 'es2015',
      sourcemap: process.env.SOURCE_MAP === "true",
      cssTarget: 'chrome80',
      minify: 'terser',
      chunkSizeWarningLimit: 2048,
      rollupOptions: {
        output: {
          entryFileNames: "assets/js/chunk-[name].[hash].js",
          chunkFileNames: "assets/js/chunk-[name].[hash].js",
          assetFileNames: "assets/[ext]/chunk-[name].[hash].[ext]",
          // 将 node_modules 三方依赖包最小化拆分
          manualChunks(id) {
            if (id.includes('node_modules')) {
              const paths = id.toString().split('node_modules/')
              if (paths[2]) {
                return paths[2].split('/')[0].toString()
              }

              return paths[1].split('/')[0].toString()
            }
          },
        },
      },
      terserOptions: {
        compress: {
          drop_console: true,     // 删除 console 语句
          drop_debugger: true,    // 删除 debugger 语句
        },
        mangle: {
          toplevel: true, // 混淆全局变量和函数名
        },
        format: {
          comments: false, // 删除所有注释
        }
      }
    },
    optimizeDeps: {
      include: [
        'pinia',
        'axios',
        'lodash-es',
      ],
      exclude: [
        'vant',
        '@vant/use'
      ]
    }
  }
}
