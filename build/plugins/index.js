import { resolve } from "path";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
import UnoCSS from "unocss/vite";
import AutoImport from "unplugin-auto-import/vite";
import { VantResolver } from '@vant/auto-import-resolver';
import Components from 'unplugin-vue-components/vite';
import { VueRouterAutoImports } from "unplugin-vue-router";
// import VueRouter from "unplugin-vue-router/vite";
import replace from "@rollup/plugin-replace";
import viteCompression from "vite-plugin-compression";
import { createSvgIconsPlugin } from "vite-plugin-svg-icons";

import { configHtmlPlugin } from './html';
import { configPwaConfig, virtualMessagePlugin } from "./pwa";
import { configVisualizerConfig } from "./visualizer";

export function createVitePlugins(viteEnv, isBuild) {
  const vitePlugins = [
    vue(),
    vueJsx(),
    // https://github.com/antfu/unplugin-vue-components
    UnoCSS(),
    Components({
      extensions: ['vue'],
      resolvers: [VantResolver()],
      include: [/\.vue$/, /\.vue\?vue/,/\.[tj]sx?$/],
      dts: false // Disable auto-generation of components.d.ts
    }),
    // // https://github.com/antfu/unplugin-auto-import
    AutoImport({
      include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/],
      imports: [
        "vue",
        "vue-router",
        "@vueuse/core",
      ],
      resolvers: [VantResolver()],
      dts: "src/auto-imports.d.ts",
      // dirs: [
      //   'src/composables',
      // ],
    }),
  ];

  // vite-plugin-svg-icons
  vitePlugins.push(
    createSvgIconsPlugin({
      iconDirs: [resolve(process.cwd(), "src/icons")],
      symbolId: "icon-[dir]-[name]",
    }),
  );

  vitePlugins.push(
    replace({
      preventAssignment: true,
      values: {
        __APP_VERSION__: JSON.stringify(process.env.npm_package_version),
        __DATE__: new Date().toISOString(),
        __RELOAD_SW__: viteEnv.RELOAD_SW === true ? "true" : "false",
      },
    }),
  );

  // 加载 html 插件 vite-plugin-html
  vitePlugins.push(configHtmlPlugin(viteEnv, isBuild))

  // rollup-plugin-visualizer
  vitePlugins.push(configVisualizerConfig(viteEnv));

  vitePlugins.push(virtualMessagePlugin());

  // vite-plugin-pwa
  vitePlugins.push(configPwaConfig(viteEnv, isBuild));

  if (isBuild) {
    //vite-plugin-imagemin
    // VITE_USE_IMAGEMIN && vitePlugins.push(configImageminPlugin());

    // rollup-plugin-gzip
    vitePlugins.push(viteCompression());
  }

  return vitePlugins;
}
