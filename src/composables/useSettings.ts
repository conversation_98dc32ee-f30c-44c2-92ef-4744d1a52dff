import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import { useSettingsStore } from '@/store/settings'

export function useSettings() {
  const settingsStore = useSettingsStore()
  const { backgroundOpacity, fontSize } = storeToRefs(settingsStore)

  // Computed properties for easy access
  const chatStyle = computed(() => (settingsStore as any).chatStyle)
  const activeSettingsKeys = computed(() => (settingsStore as any).activeSettingsKeys)

  // Check if specific settings are active
  const isGiftEffectActive = computed(() => (settingsStore as any).isSettingActive('gift'))
  const isSoundActive = computed(() => (settingsStore as any).isSettingActive('sound'))
  const isVibrationActive = computed(() => (settingsStore as any).isSettingActive('vibration'))
  const isMessageActive = computed(() => (settingsStore as any).isSettingActive('message'))
  const isStealthActive = computed(() => (settingsStore as any).isSettingActive('stealth'))
  const isInvisibleActive = computed(() => (settingsStore as any).isSettingActive('invisible'))
  const isReminderActive = computed(() => (settingsStore as any).isSettingActive('reminder'))
  const isSpecialActive = computed(() => (settingsStore as any).isSettingActive('special'))

  // Actions
  const toggleSetting = (key: string) => (settingsStore as any).toggleSetting(key)
  const setSetting = (key: string, isActive: boolean) => (settingsStore as any).setSetting(key, isActive)
  const setBackgroundOpacity = (opacity: number) => (settingsStore as any).setBackgroundOpacity(opacity)
  const setFontSize = (size: number) => (settingsStore as any).setFontSize(size)
  const resetSettings = () => (settingsStore as any).resetSettings()
  const applyChatSettings = (element: HTMLElement) => (settingsStore as any).applyChatSettings(element)

  // Utility functions
  const shouldShowGiftEffect = () => isGiftEffectActive.value
  const shouldPlaySound = () => isSoundActive.value
  const shouldVibrate = () => isVibrationActive.value
  const shouldShowMessage = () => isMessageActive.value
  const shouldUseStealthMode = () => isStealthActive.value
  const shouldHideFromLeaderboard = () => isInvisibleActive.value
  const shouldShowReminder = () => isReminderActive.value
  const shouldShowSpecialEffect = () => isSpecialActive.value

  return {
    // Store instance
    settingsStore,
    
    // Computed properties
    backgroundOpacity,
    fontSize,
    chatStyle,
    activeSettingsKeys,
    
    // Setting states
    isGiftEffectActive,
    isSoundActive,
    isVibrationActive,
    isMessageActive,
    isStealthActive,
    isInvisibleActive,
    isReminderActive,
    isSpecialActive,
    
    // Actions
    toggleSetting,
    setSetting,
    setBackgroundOpacity,
    setFontSize,
    resetSettings,
    applyChatSettings,
    
    // Utility functions
    shouldShowGiftEffect,
    shouldPlaySound,
    shouldVibrate,
    shouldShowMessage,
    shouldUseStealthMode,
    shouldHideFromLeaderboard,
    shouldShowReminder,
    shouldShowSpecialEffect
  }
} 