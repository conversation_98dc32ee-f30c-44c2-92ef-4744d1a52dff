import { storeToRefs } from 'pinia';
import { param2Obj } from '@/utils';
import { useService } from '@/hooks';
import { useUserStore } from '@/store/user';
import { useAppStore } from '@/store/app';

export function useNavigate() {
  const route = useRoute();
  const router = useRouter();

  const appStore = useAppStore();
  const userStore = useUserStore();

  const { type } = storeToRefs(userStore);

  const navigatorWithdraw = () => {
    if (type.value !== 0) {
      router.push({ path: '/withdraw' })
    } else {
      appStore.SetHasNeedPhoneAction(true);
    }
  };

  const navigateTo = (content, jumpType) => {
    // 跳转地址
    if (jumpType === 1) {
      const [_, identifier] = content.match(/app\:\/\/([a-z_]*)\?/) || [];

      function goPage() {
        var { url, target, title } = param2Obj(content);
        if (target === '0') {
          router.push({ path: '/webview', query: { url: encodeURIComponent(url), title } });
        } else {
          window.dsBridge?.call('app.openExternalApp', url);
        }
      }
  
      switch (identifier) {
        case 'web':
          goPage();
          break;
        case 'service':
          useService();
          break;
        case 'share':
          router.push({ path: '/promotion' });
          break;
        case 'pay_diamond':
          router.push({ path: '/cz' });
          break;
        case 'quik_login_chess':
          const { game_id, platform_id, cut, horizontal } = param2Obj(content);
          router.push({ path: '/webview_game', query: { game_id, platform_id, cut, horizontal, from: route.fullPath } });
          break;
        case 'native_game':
          router.push({ path: '/yx' });
          break;
        case 'native_game_page':
          const { cate_id } = param2Obj(content);
          router.push({ path: '/yx', query: { cate_id } });
          break;
        case 'native_game_all_page':
          const { column_id, title } = param2Obj(content);
          router.push({ path: '/game_category', query: { title, column_id } });
          break;
        case 'vi_wallet':
          break;
        case 'task':
          const { type_id, tab_index } = param2Obj(content);
          router.push({ path: '/task_detail', query: { taskTypeId: type_id } });
          break;
        case 'bindphone':
          router.push({ path: '/bind_phone' });
          break;
        case 'novice_task':
          router.push({ path: '/newbietask', query: { taskTypeId: 0 } });
          break;
        case 'withdraw':
          navigatorWithdraw();
          break;
        case 'activity':
          const { activity_id } = param2Obj(content);
          router.push({ path: '/hd', query: { activity_id } });
          break;
        case 'activity_detail':
          const { activity_list_id } = param2Obj(content);
          router.push({ path: '/webview_activity', query: { activity_id: activity_list_id } });
          break;
        case 'private_game':
          const { game_id: privateGameId } = param2Obj(content);
          appStore.SetVideoLotteryPopupAction({
            show: true,
            gameId: parseInt(privateGameId)
          });
          break;
        default:
          return;
      }
    } else if (jumpType === 2) {
      // 富文本跳转
      router.push({ path: '/webview_html', query: { content: encodeURIComponent(content) } });
    }
  };

  return { navigateTo };
}