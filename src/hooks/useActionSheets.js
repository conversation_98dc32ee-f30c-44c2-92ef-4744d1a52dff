import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

export const useActionSheets = () => {

  const SHEET_TYPES = {
    PROFILE: 'profile',
    LEADERBOARD: 'leaderboard',
    WATCHLIST: 'watchlist',
    VIPMEMBERS: 'vipmembers',
    SETTINGS: 'settings'
  }

  const route = useRoute()
  const router = useRouter()

  const showProfileSheet = ref(false)
  const showLeaderboardSheet = ref(false)
  const showWatchListSheet = ref(false)
  const showVipMembersSheet = ref(false)
  const showSettingsSheet = ref(false)

  const handleSheetOpen = (viewType) => {
    showProfileSheet.value = false
    showLeaderboardSheet.value = false
    showWatchListSheet.value = false
    showVipMembersSheet.value = false
    showSettingsSheet.value = false

    switch (viewType) {
      case SHEET_TYPES.PROFILE:
        showProfileSheet.value = true
        break
      case SHEET_TYPES.LEADERBOARD:
        showLeaderboardSheet.value = true
        break
      case SHEET_TYPES.WATCHLIST:
        showWatchListSheet.value = true
        break
      case SHEET_TYPES.VIPMEMBERS:
        showVipMembersSheet.value = true
        break
      case SHEET_TYPES.SETTINGS:
        showSettingsSheet.value = true
        break
      default:
        break
    }
  }

  const openProfile = () => {
    router.push({ path: route.path, query: { ...route.query, view: SHEET_TYPES.PROFILE } })
  }

  const openLeaderboard = () => {
    router.push({ path: route.path, query: { ...route.query, view: SHEET_TYPES.LEADERBOARD } })
  }

  const openWatchList = () => {
    router.push({ path: route.path, query: { ...route.query, view: SHEET_TYPES.WATCHLIST } })
  }

  const openVipMembers = () => {
    router.push({ path: route.path, query: { ...route.query, view: SHEET_TYPES.VIPMEMBERS } })
  }

  const openSettings = () => {
    router.push({ path: route.path, query: { ...route.query, view: SHEET_TYPES.SETTINGS } })
  }

  watch(
    () => route.query.view,
    (viewType) => {
      handleSheetOpen(viewType)
    },
    { immediate: true }
  )

  watch([showProfileSheet, showLeaderboardSheet, showWatchListSheet, showVipMembersSheet, showSettingsSheet], ([profile, leaderboard, watchlist, vipmembers, settings]) => {
    if (!profile && !leaderboard && !watchlist && !vipmembers && !settings) {
      const { view, ...otherQuery } = route.query
      if (view) {
        router.push({ path: route.path, query: otherQuery })
      }
    }
  })

  const addSheetType = (name, sheetRef) => {
    SHEET_TYPES[name.toUpperCase()] = name.toLowerCase()
    
    const originalHandleSheetOpen = handleSheetOpen
    handleSheetOpen = (viewType) => {
      originalHandleSheetOpen(viewType)
      if (viewType === name.toLowerCase()) {
        sheetRef.value = true
      }
    }
  }

  return {
    // States
    showProfileSheet,
    showLeaderboardSheet,
    showWatchListSheet,
    showVipMembersSheet,
    showSettingsSheet,
    
    // Constants
    SHEET_TYPES,
    
    // Actions
    openProfile,
    openLeaderboard,
    openWatchList,
    openVipMembers,
    openSettings,
    handleSheetOpen,
    
    // Utilities
    addSheetType
  }
} 