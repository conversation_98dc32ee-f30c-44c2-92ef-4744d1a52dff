import { uGetTelCode } from '@/api/user';

export const useCaption = (loginForm, paramsCb) => {
  const t = ref(60);

  const isPending = ref(false);

  const { pause, resume, isActive } = useIntervalFn(
    () => {
      if (t.value > 1) {
        t.value -= 1;
      } else {
        isPending.value = false;
        pause();
      }
    },
    1000,
    { immediate: false }
  );

  const getCaption = () => {
    if (isActive.value) return;
    loginForm.value
      .validate('tel')
      .then(() => {
        console.log('getCaption pass');
        const toast = showLoadingToast({ duration: 0 });
        const params = paramsCb()
        uGetTelCode(params)
          .then(() => {
            isPending.value = true;
            resume();
            showToast({
              message: '发送成功',
              duration: 1000
            });
          })
          .finally(() => {
            toast.close();
          });
      })
      .catch((error) => {
        isPending.value = false;
        showToast({
          message: error.message,
          icon: 'info-o'
        });
      });
  };

  return {
    t,
    isActive,
    isPending,
    getCaption
  }
}