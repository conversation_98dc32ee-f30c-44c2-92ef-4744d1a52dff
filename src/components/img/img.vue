<template>
  <lazy-component @show="imageOnLoad" :class="props.hasBackground ? 'LazyLoad_image' : 'LazyLoad'">
    <img
      ref="imgRef"
      :class="hasAnimation ? styles.img : styles.img_not_animation"
      :style="{
        width: '100%',
        height: '100%',
        objectFit: props.objectFit
      }"
      :src="BrokenImage"
      :data-url="imgUrl"
      alt="img"
    />
  </lazy-component>
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { getImageBase64 } from '@/utils/decrypto';
import { key, iv } from "@/constants/img";
import styles from './img.module.css'
import './img.css'


const BrokenImage = new URL(`/theme/${import.meta.env.VITE_APP_MODE}/img/placeholder.jpg`, import.meta.url).href


defineOptions({
  name: 'ImgComponents'
})

const props = defineProps({
  imgUrl: String,
  hasBackground: Boolean,
  hasAnimation: Boolean,
  objectFit: {
    type: String,
    default: 'cover'
  }
})

const emit = defineEmits(['load']);



const imgRef = ref(null)
const loading = ref(true)
const isLoad = ref(false)

const imageOnLoad = (el) => {
  if (isLoad.value) return

  nextTick(() => {
    let data = imgRef.value?.getAttribute('data-url');
    getImageBase64(data, key, iv)
      .then((res) => {
        imgRef.value.src = res;
        props.hasAnimation ? imgRef.value.classList.add('image') : imgRef.value.classList.add('image-not-animation');
        emit('load')
      })
      .catch(() => {
        if (imgRef.value) {
          imgRef.value.src = BrokenImage;
        }
      })
      .finally(() => {
        loading.value = false
        isLoad.value = true
      });
  })
};

watch(
  () => props.imgUrl,
  () => {
    isLoad.value = false;
    imageOnLoad()
  }
)

</script>
