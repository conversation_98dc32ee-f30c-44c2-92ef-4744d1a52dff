<template>
  <div>
    <!-- Country/Region Selector -->
    <div class="w-11/12 mx-auto mt-16 mb-4">
      <div class="flex items-center justify-between">
        <span class="text-white text-base">国际和地区</span>
        <div 
          class="flex items-center bg-[#1F1F1F] rounded-full px-4 py-2 cursor-pointer"
          @click="showCountrySheet = true"
        >
          <img src="@/assets/webp/icon/icon-pin.webp" alt="" class="w-4 h-4 object-contain mr-2" />
          <span class="text-white">{{ selectedCountry.name }}</span>
          <img src="@/assets/webp/icon/icon-arrow.webp" alt="" class="w-3 h-3 object-contain ml-2">
        </div>
      </div>
    </div>

    <!-- Country Selection Sheet -->
    <van-popup
      v-model:show="showCountrySheet"
      :overlay="false"
      position="bottom"
      round
      safe-area-inset-bottom
      style="background-color: var(--van-black-500);"
    >

      <div class="flex flex-col h-577px" style="--van-search-background: transparent; --van-search-content-background: rgba(191, 191, 191, .15);">
        <h2 class="pt-6 pb-2 pl-7">国际和地区</h2>
        <van-search
          style="--van-search-left-icon-color: #FFD631;"
          v-model="searchQuery"
          placeholder="搜索"
          shape="round"
          input-align="left"
        />

        <div class="country-list pl-4 pr-6 flex-1 overflow-auto">
          <div v-if="isSearching" class="flex items-center justify-center h-20">
            <van-loading size="20px" color="#ffd631">搜索中...</van-loading>
          </div>
          <van-index-bar 
            v-else
            :sticky="false"
            :index-list="indexList"
            :style="{ 
              '--van-index-bar-index-font-size': '12px',
              '--van-index-bar-index-active-color': '#ffd631'
            }"
          >
            <template v-for="(countriesInGroup, group) in groupedCountries" :key="group">
              <van-index-anchor :index="group" />
              <van-cell 
                v-for="country in countriesInGroup" 
                :key="country.code"
                :title="country.name"
                clickable
                @click="selectCountry(country)"
                :style="getCellStyle(country.code)"
              />
            </template>
          </van-index-bar>
        </div>
        
        <!-- Action Buttons -->
        <div class="flex gap-4 p-4">
          <van-button 
            block 
            round
            color="#474126"
            style="color: #FFD631;"
            @click="cancelSelection"
            class="flex-1 text-white border-gray-600"
          >
            取消
          </van-button>
          <van-button 
            block 
            round
            type="primary"
            @click="confirmCountrySelection"
            class="flex-1 bg-[var(--van-primary-color)] text-black border-[var(--van-primary-color)]"
          >
            确定
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
import countriesData from '@/assets/json/countries_mapped.json';

// Props & Emits
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({ name: '中国', code: 86 })
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const showCountrySheet = ref(false);
const searchQuery = ref('');
const selectedCountry = ref({ ...props.modelValue });
const tempSelectedCountry = ref({ ...props.modelValue });
const isSearching = ref(false);

const countries = countriesData;

const filteredCountries = computed(() => {
  if (!searchQuery.value) return countries;
  
  return countries.filter(country =>
    country.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    country.code.toString().includes(searchQuery.value) ||
    (`+${country.code}`).includes(searchQuery.value)
  );
});

const groupedCountries = computed(() => {
  const groups = {};
  
  filteredCountries.value.forEach(country => {
    const group = country.group;
    if (!groups[group]) {
      groups[group] = [];
    }
    groups[group].push(country);
  });
  
  const sortedGroups = {};
  Object.keys(groups).sort().forEach(group => {
    sortedGroups[group] = groups[group].sort((a, b) => a.name.localeCompare(b.name));
  });
  
  return sortedGroups;
});

const indexList = computed(() => {
  return Object.keys(groupedCountries.value);
});

const selectCountry = (country) => {
  tempSelectedCountry.value = { ...country };
};

const confirmCountrySelection = () => {
  selectedCountry.value = { ...tempSelectedCountry.value };
  emit('update:modelValue', selectedCountry.value);
  emit('change', selectedCountry.value);
  showCountrySheet.value = false;
};

const cancelSelection = () => {
  tempSelectedCountry.value = { ...selectedCountry.value };
  showCountrySheet.value = false;
};

const getCellStyle = (code) => {
  return tempSelectedCountry.value.code === code ? {
    backgroundColor: '#403B24',
    color: '#fff',
    borderRadius: '40px',
    border: '1px solid #ffd631'
  } : {
    backgroundColor: '#1F1F1F',
    color: '#fff',
    borderWidth: '1px solid transparent',
  };
};

watch(searchQuery, () => {
  isSearching.value = false;
});

watch(() => props.modelValue, (newValue) => {
  selectedCountry.value = { ...newValue };
  tempSelectedCountry.value = { ...newValue };
}, { immediate: true });
</script>

<style>
.country-list .van-index-bar__sidebar {
  position: absolute;
}

.country-list .van-cell {
  /* padding: 6px 16px; */
}
.country-list .van-cell:after {
  display: none;
}
</style>