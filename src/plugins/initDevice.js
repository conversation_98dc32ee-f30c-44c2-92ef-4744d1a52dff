import fingerPrint from '@fingerprintjs/fingerprintjs';
import MD5 from 'js-md5';
export default {
  install (app, options = {}) {
    return new Promise((resolve) => {
      fingerPrint.load()
        .then(fp => fp.get())
        .then(result => {
          const visitorId = result.visitorId;
          const uuid = MD5(visitorId + '&' + import.meta.env.VITE_APP_SITE);
          localStorage.setItem('deviceId', uuid)
          resolve()
        })
    })
  }
}