import { defineStore } from 'pinia';
import { PersonInfoDataAPI } from '@/api/user';
import { GetWalletAPI } from '@/api/game';
import { removeLocalStorage } from '@/utils';
import { store } from '@/store';

const { VITE_TOKEN_KEY, VITE_PLAY_USER_KEY } = import.meta.env;

export const useUserStore = defineStore('user', {
  state: () => ({
    loded: false,
    token: '',
    id: '',
    uuid: '',
    nickName: '',
    birthDay: '',
    picUrl: '',
    ac: '',
    tel: '',
    isVip: false,
    vipId: 0,
    vipIcon: '',
    vipLevel: 0,
    vipValidTime: 0,
    type: null,
    channelId: null,
    createTimeStr: '',
    updateTimeStr: '',
    subchanelId: '',
    rip: '',
    lip: '',
    kk: '',
    kid: '',
    isRegister: 0,
    device: 3,
    birthDay: '',
    telPre: '',
    inviteCode: '',
    is_enable: 0,
    is_set_payment_password: 0,
    is_set_username: 0,
    ac: '',
    longVideoFreeWatchLeft: 0,
    shortVideoFreeWatchLeft: 0,
    wallet: {
      points: '0.00',
      quotas: '0.00',
      diamonds: '0.00',
      sent_diamonds: '0'
    }
  }),
  actions: {
    updateUserInfo(payload) {
      this.token = payload.token;
      this.id = payload.id;
      this.uuid = payload.uuid;
      this.nickName = payload.nickName;
      this.birthDay = payload.birthDay;
      this.picUrl = payload.picUrl || '1';
      this.ac = payload.ac;
      this.tel = payload.tel;
      this.isVip = payload.isVip;
      this.vipId = payload.vipId;
      this.vipIcon = payload.vipIcon;
      this.vipLevel = payload.vipLevel;
      this.vipValidTime = payload.vipValidTime;
      this.type = payload.type;
      this.channelId = payload.channelId;
      this.createTimeStr = payload.createTimeStr;
      this.updateTimeStr = payload.updateTimeStr;
      this.subchanelId = payload.subchanelId;
      this.rip = payload.rip;
      this.lip = payload.lip;
      this.kk = payload.kk;
      this.kid = payload.kid;
      this.isRegister = payload.isRegister;
      this.device = payload.device;
      this.birthDay = payload.birthDay;
      this.telPre = payload.telPre;
      this.inviteCode = payload.inviteCode;
      this.is_set_payment_password = payload.is_set_payment_password;
      this.is_set_username = payload.is_set_username;
      this.ac = payload.ac;
      this.longVideoFreeWatchLeft = payload.longVideoFreeWatchLeft;
      this.shortVideoFreeWatchLeft = payload.shortVideoFreeWatchLeft;
    },

    updateUserWalletHandle(payload) {
      this.wallet = payload;
    },

    updatePersonDataField({ filed, value }) {
      this[filed] = value;
    },

    logoutHandle() {
      this.loded = false;
      this.token = '';
      removeLocalStorage(VITE_TOKEN_KEY);
      removeLocalStorage(VITE_PLAY_USER_KEY);
    },

    async updatePersonData() {
      try {
        const res = await PersonInfoDataAPI();
        this.updateUserInfo(res.data);
        this.loded = true;
        return res;
      } catch (e) {
        //
      }
    },

    async updateUserWalletData() {
      const res = await GetWalletAPI();
      console.log(res, 'user wallet res');
      this.updateUserWalletHandle(res.data);
      return res;
    }
  }
});

// Need to be used outside the setup
export function useUserStoreWidthOut() {
  return useUserStore(store);
}
