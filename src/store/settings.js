import { defineStore } from 'pinia';
import { store } from '@/store';
import { DEFAULT_SETTINGS, DEFAULT_CHAT_SETTINGS } from '@/constants/settings';

export const useSettingsStore = defineStore('settings', {
  persist: {
    paths: ['backgroundOpacity', 'fontSize', 'settings']
  },
  state: () => ({
    // Chat settings
    backgroundOpacity: DEFAULT_CHAT_SETTINGS.backgroundOpacity,
    fontSize: DEFAULT_CHAT_SETTINGS.fontSize,
    
    // Settings status
    settings: { ...DEFAULT_SETTINGS }
  }),
  
  getters: {
    // Check if a specific setting is active
    isSettingActive: (state) => (key) => state.settings[key] || false,
    
    // Get all active settings keys
    activeSettingsKeys: (state) => Object.keys(state.settings).filter(key => state.settings[key]),
    
    // Get chat style based on settings
    chatStyle: (state) => ({
      backgroundColor: `rgba(0, 0, 0, ${state.backgroundOpacity / 100})`,
      fontSize: `${state.fontSize}px`
    })
  },
  
  actions: {
    // Toggle a specific setting
    toggleSetting(key) {
      if (key in this.settings) {
        this.settings[key] = !this.settings[key];
      }
    },
    
    // Set a specific setting state
    setSetting(key, isActive) {
      if (key in this.settings) {
        this.settings[key] = isActive;
      }
    },
    
    // Update background opacity
    setBackgroundOpacity(opacity) {
      this.backgroundOpacity = Math.max(0, Math.min(100, opacity));
    },
    
    // Update font size
    setFontSize(size) {
      this.fontSize = Math.max(12, Math.min(32, size));
    },
    
    // Reset all settings to default
    resetSettings() {
      this.backgroundOpacity = DEFAULT_CHAT_SETTINGS.backgroundOpacity;
      this.fontSize = DEFAULT_CHAT_SETTINGS.fontSize;
      
      // Reset all settings to default states
      this.settings = { ...DEFAULT_SETTINGS };
    },
    
    // Apply settings to elements (utility function)
    applyChatSettings(element) {
      if (element) {
        element.style.backgroundColor = `rgba(0, 0, 0, ${this.backgroundOpacity / 100})`;
        element.style.fontSize = `${this.fontSize}px`;
      }
    }
  }
});

// Need to pass the store instance to be used outside of components
export function useSettingsStoreWithOut() {
  return useSettingsStore(store);
} 