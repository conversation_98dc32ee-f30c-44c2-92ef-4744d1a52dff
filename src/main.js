import { registerSW } from 'virtual:pwa-register'
import "virtual:svg-icons-register";
import 'virtual:uno.css';
import "dsbridge";
import { createApp } from "vue";
import { VueQueryPlugin } from '@tanstack/vue-query';

import App from "./App.vue";
import reportWebVitals from './reportWebVitals';
import router, { setupRouter } from "@/router";
import { setupStore } from '@/store';
import InitAPPData from "@/plugins/initialAppData";
import { setupVant } from "@/plugins/Vant.js";
import { setupGlobCom } from "@/components";

import "./styles/index.less";

registerSW({
  onNeedRefresh() {
    console.log("检测到新版本，即将更新...");
    // updateSW(true); // 立即更新 Service Worker
  },
  onOfflineReady() {
    console.log("PWA 已准备好离线使用");
  }
})

async function bootstrap() {
  const app = createApp(App);

  await InitAPPData.install(app);

  // Setup Vue Query with default configuration
  app.use(VueQueryPlugin, {
    queryClientConfig: {
      defaultOptions: {
        queries: {
          staleTime: 1000 * 60 * 5, // 5 minutes
          cacheTime: 1000 * 60 * 10, // 10 minutes
          retry: 2,
          refetchOnWindowFocus: false,
        },
        mutations: {
          retry: 1,
        },
      },
    },
  });

  setupVant(app);
  setupGlobCom(app);
  setupStore(app);
  setupRouter(app);
  await router.isReady()
  app.mount("#app", true);
}

bootstrap();

if (import.meta.env.VITE_APP_VITALS === 'true') {
  reportWebVitals();
}


