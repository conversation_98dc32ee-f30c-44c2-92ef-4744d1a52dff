<template>
  <SecondPageLayout>
    <div
      class="flex items-center w-[345px] h-[75px] mx-auto bg-[var(--van-black-500)] rounded-md box-border px-3 mt-5"
      @click="navigatorHandle('/profile_data')"
    >
      <div class="overflow-hidden mr-2.5">
        <img src="@/assets/icons/info.png" class="w-[34px] h-[34px]" />
      </div>
      <div class="flex-1">
        <p class="text-sm">个人资料</p>
        <span class="text-[.625rem] text-[#cccccc]">完善个人资料</span>
      </div>
      <van-icon name="arrow" />
    </div>

    <div
      class="flex items-center w-[345px] h-[75px] mx-auto bg-[var(--van-black-500)] rounded-md box-border px-3 mt-5"
      @click="navigateBindPhone"
    >
      <div class="overflow-hidden mr-2.5">
        <img src="@/assets/icons/ph.png" class="w-[34px] h-[34px]" />
      </div>
      <div class="flex-1">
        <p class="text-sm">手机号验证码</p>
        <span class="text-[.625rem] text-[#cccccc]">绑定手机号码，保护你的财产安全</span>
      </div>
      <div class="text-xs mr-2">
        <span v-if="type !== 0" class="text-[var(--van-primary-color)]">{{ formatTel(tel) }}</span>
        <span v-else class="text-[var(--van-primary-color)]">去绑定</span>
      </div>
      <van-icon name="arrow" />
    </div>

    <div
      class="flex items-center w-[345px] h-[75px] mx-auto bg-[var(--van-black-500)] rounded-md box-border px-3 mt-5"
      @click="navigatorPassword"
    >
      <div class="overflow-hidden mr-2.5">
        <img src="@/assets/icons/user.png" class="w-[34px] h-[34px]" />
      </div>
      <div class="flex-1">
        <p class="text-sm">设置账号密码</p>
        <span class="text-[.625rem] text-[#cccccc]">定期修改密码有利于账户安全</span>
      </div>
      <van-icon name="arrow" />
    </div>

    <div class="flex flex-col w-[345px] mx-auto bg-[var(--van-black-500)] rounded-md box-border px-3 mt-5">
      <!-- <div class="flex items-center justify-between h-10">
        <div class="text-sm">
          <span>清除缓存</span>
        </div>
        <div></div>
      </div> -->
      <div class="flex items-center justify-between h-10" @click="updateHandle">
        <div class="text-sm">
          <span>版本检测</span>
        </div>
        <div class="text-xs">
          <span class="mr-3">{{ version }}</span>
          <van-icon name="arrow" />
        </div>
      </div>
    </div>

    <div class="w-[260px] mx-auto mt-8">
      <van-button type="primary" block @click="switchHandle">切换账号</van-button>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useAppStore } from '@/store/app';
import { useUserStore } from '@/store/user';
import { useRegisterSW } from 'virtual:pwa-register/vue';

const version = __APP_VERSION__;

const appStore = useAppStore();
const userStore = useUserStore();

const router = useRouter();

const { needRefresh, updateServiceWorker } = useRegisterSW()

const { type, tel, is_set_username } = storeToRefs(userStore);

const formatTel = (str) => {
  return str.replace(/^1[3-9]\d{9}$/, (phone) => phone.replace(/(\d{3})\d*(\d{4})/, '$1****$2'))
}

const navigatorHandle = (path) => {
  router.push({ path });
};

const navigateBindPhone = () => {
  if (type.value !== 0) {
    navigatorHandle('/bind_phone_success');
  } else {
    navigatorHandle('/bind_phone');
  }
};

const navigatorPassword = () => {
  if (is_set_username.value === 0) {
    navigatorHandle('/password_setting');
  } else {
    navigatorHandle('/password_reset');
  }
};

// const showPrompt = ref(false);

// const cancelHandler = () => {
//   showPrompt.value = false;
// }

// const submitHandler = () => {
//   showPrompt.value = false;
//   useStore.logoutHandle();
//   router.replace({ path: "/" });
// }

const switchHandle = () => {
  appStore.SetHasNeedLoginAction(true);
};

const updateHandle = () => {
  if (needRefresh) {
    updateServiceWorker();
  } else {
    showToast('您当前版本为最新版本');
  }
}
</script>
