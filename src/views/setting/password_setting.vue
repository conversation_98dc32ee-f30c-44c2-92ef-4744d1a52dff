<template>
  <SecondPageLayout>
    <van-form @submit="onSubmit" @failed="onFailed" :show-error-message="false" class="mt-6" style="--van-cell-border-color: #4c4c4c;">
      <van-cell-group inset>
        <van-field
          v-model="params.username"
          label="账号"
          type="text"
          name="username"
          size="large"
          autocomplete="off"
          placeholder="请设置您的账号"
          :rules="[{ required: true, message: '请设置您的账号' }]"
        />
        <van-field
          v-model="params.newPwd"
          label="密码"
          type="password"
          name="newPwd"
          size="large"
          autocomplete="off"
          placeholder="请输入您的密码"
          :rules="[{ required: true, message: '请输入您的密码' }]"
        />
        <van-field
          v-model="params.repeatNewPwd"
          label="确认密码"
          type="password"
          name="repeatNewPwd"
          size="large"
          autocomplete="off"
          placeholder="请输入您的密码"
          :rules="[{ required: true, message: '请输入您的密码' }]"
        />
      </van-cell-group>

      <div class="w-260px mx-auto mt-18">
        <van-button type="primary" round block native-type="submit"> 确认 </van-button>
      </div>

    </van-form>

    
  </SecondPageLayout>
</template>

<script setup>
import { useUserStore } from '@/store/user';
import { SetupPassword } from '@/api/user';

defineOptions({
  name: 'PasswordReset'
});
const userStore = useUserStore();
const router = useRouter();

// Form parameters
const params = reactive({
  username: '',
  newPwd: '',
  repeatNewPwd: '',
});

const validatorPassword = (val) => {
  new Promise((resolve) => {
    resolve(val === params.newPwd);
  });
}

const onFailed = ({ errors }) => {
  showToast(errors?.[0].message);
}
// Handle form submission
const onSubmit = async () => {
  const toast = showLoadingToast();

  try {
    const { data } = await SetupPassword({
      username: params.username,
      newPwd: params.newPwd,
      repeatNewPwd: params.repeatNewPwd
    });
    userStore.updateUserInfo(data);
    router.back();
  } catch (error) {
    //
  } finally {
    toast.close();
  }
};
</script>

