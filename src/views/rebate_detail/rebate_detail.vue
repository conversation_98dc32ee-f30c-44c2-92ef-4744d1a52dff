<template>
  <SecondPageLayout>
    <div class="w-full flex items-stretch text-sm text-center text-[#2f2f2f] font-medium py-2.5 bg-[var(--van-primary-color)] rebate_table_head">
      <p class="flex flex-1 items-center justify-center">等级</p>
      <p class="flex flex-1 items-center justify-center">直播间彩票</p>
      <p class="flex flex-1 items-center justify-center">棋牌</p>
      <p class="flex flex-1 items-center justify-center">真人</p>
      <p class="flex flex-1 items-center justify-center">捕鱼</p>
      <p class="flex flex-1 items-center justify-center">电子</p>
      <p class="flex flex-1 items-center justify-center">体育</p>
    </div>


    <table class="w-full text-xs text-center rebate_table font-medium">
      <tbody>
        <tr v-for="val in list" :key="val.level" class="h-34px">
          <td class="text-[var(--van-primary-color)]">SVIP{{ val.level }}</td>
          <td>{{ val.localGameRate }}</td>
          <td>{{ val.chessGameRate }}</td>
          <td>{{ val.videoGameRate }}</td>
          <td>{{ val.fishGameRate }}</td>
          <td>{{ val.slotGameRate }}</td>
          <td>{{ val.sportGameRate }}</td>
        </tr>
      </tbody>
    </table>

    <p class="text-center text-lg my-6 text-[var(--van-primary-color)]">返水规则</p>

    <div class="whitespace-pre-line px-4 pb-8">{{ info }}</div>

  </SecondPageLayout>
</template>

<script setup>
import { GetRebateRateAPI } from '@/api/promotion';

const info = shallowRef('');
const list = shallowRef([]);

const GetRebateRate = async () => {
  try {
    const { data } = await GetRebateRateAPI();
    info.value = data.info;
    list.value = data.items || [];
  } catch (e) {
    //
  }
}

onMounted(() => {
  GetRebateRate();
})

</script>

<style lang="less">

.rebate_table_head {
  & > p:not(:last-child) {
    border-right: 1px solid #666;
  }
}

.rebate_table {
  table-layout: fixed;
  border-collapse: collapse; /* 合并单元格边框 */
  border: 1px solid #666; /* 表格整体边框 */
  tr:nth-child(2n) {
    background-color: #333;
  }
  td {
    border: 1px solid #666; /* 单元格边框 */
  }
}
</style>