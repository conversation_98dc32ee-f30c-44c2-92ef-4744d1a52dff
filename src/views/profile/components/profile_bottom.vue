<template>
  <div class="rounded bg-[var(--van-black-500)] my-2.5">
    <div class="h-10 flex items-center pl-5 relative" @click="navigateHandle(1)">
      <img :src="passwordIcon" class="w-4" />
      <span class="text-white text-xs pl-3">支付密码</span>
    </div>
    <div class="h-10 flex items-center pl-5 relative" @click="navigateHandle(2)">
      <img :src="business" class="w-4" />
      <span class="text-white text-xs pl-3">商务合作</span>
    </div>
    <!-- <div class="h-10 flex items-center pl-5 relative" @click="navigateHandle(3)">
      <img :src="freeback" class="w-4" />
      <span class="text-white text-xs pl-3">意见反馈</span>
    </div> -->
    <div class="h-10 flex items-center pl-5 relative" @click="navigateHandle(5)">
      <img :src="about" class="w-4" />
      <span class="text-white text-xs pl-3">关于我们</span>
    </div>
  </div>

  <van-action-sheet
    v-model:show="show"
    :actions="actions"
    cancel-text="取消"
    close-on-click-action
    @select="onSelect"
    teleport="body"
  />
</template>

<script setup>
import { storeToRefs } from 'pinia';
import { useUserStore } from "@/store/user";
import store from '@/assets/webp/store.webp';
import passwordIcon from '@/assets/icons/icon_password.png';
import freeback from '@/assets/webp/freeback.webp';
import business from '@/assets/webp/business.webp';
import about from '@/assets/webp/about.webp';

const actions = [
{ name: '修改密码', value: 1 },
{ name: '找回密码', value: 2 },
]

const router = useRouter();

const useStore = useUserStore();

const show = ref(false);

const { type, is_set_payment_password } = storeToRefs(useStore);

const navigatePassword = () => {
  if (is_set_payment_password.value === 0) {
    router.push({ path: "/pay_password_set" });
  } else {
    show.value = true;
  }
};

const onSelect = ({ name, value }) => {
  switch (value) {
    case 1:
      router.push({ path: "/pay_password_reset" });
      return;
    case 2:
      router.push({ path: "/pay_password_recover" });
      return;
  }
}

const navigateHandle = (key) => {
  switch (key) {
    case 1:
      navigatePassword();
      return;
    case 2:
      router.push({ path: '/business' });
      return;
    // case 3:
    //   router.push({ path: '/feedback' });
      return;
    case 5:
      router.push({ path: '/aboutus' });
      return;
    default:
      showToast('敬请期待');
      return;
  }
};
</script>

