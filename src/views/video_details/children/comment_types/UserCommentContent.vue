<template>
  <p v-if="isBarrage">
    <img v-if="lvIcon" style="height: 22px" :src="lvIcon" class="inline mr-1" />
    <img style="height: 22px" :src="rankIconUrl" class="inline mr-1" />
    <span class="text-[#ffd631] mr-1 font-bold">{{ username }}:</span>
    {{ content }}
  </p>
  <p v-else>
    <img v-if="lvIcon" style="height: 22px" :src="lvIcon" class="inline mr-1" />
    <span class="text-[#ffd631] mr-1 font-bold">{{ username }}:</span>
    {{ content }}
  </p>
</template>

<script setup lang="ts">
import type { UserWsMessage } from '@/types/comment';
import { computed } from 'vue';
import { getRankIcon } from '@/utils/vipLv';
import { getImageBase64 } from '@/utils/decrypto';
import { key, iv } from "@/constants/img";

interface Props {
  comment: UserWsMessage;
}

const props = defineProps<Props>();

const lvIcon = ref(null);

// Extract data from WebSocket message
const isBarrage = computed(() => props.comment.data.type === 1);
// for barrage comment
const lvIconEncryped = computed(() => props.comment.data.user_info.levelIcon);
const rankIconUrl = computed(() => {
  return getRankIcon(props.comment.data.user_info.level);
});
// end for barrage comment
const username = computed(() => props.comment.data.user_info.nickname);
const content = computed(() => props.comment.data.content);

watch(
  () => lvIconEncryped.value,
  (newValue) => {
    if (newValue) {
      getImageBase64(newValue, key, iv)
        .then((res) => {
          lvIcon.value = res;
        })
        .catch(() => {
          lvIcon.value = '';
        });
    }
  },
  { immediate: true }
);
</script>

<style scoped></style>
