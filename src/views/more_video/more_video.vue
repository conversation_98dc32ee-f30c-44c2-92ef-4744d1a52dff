<template>
  <SecondPageLayout :title="title">
    <template #nav-extra>
      <ThemeSort
        v-model:desc="listParams.isDesc"
        v-model:order="listParams.orderBy"
        @change="onRefresh"
      />
    </template>

    <div class="py-2 px-3 h-full box-border">
      <van-pullRefresh v-model="refreshing" @refresh="onRefresh" class="min-h-full">
        <van-list
          v-model:loading="loading"
          v-model:error="error"
          :finished="finished"
          @load="onLoad"
        >
          <div v-if="banners && banners.length" class="h-120px" style="flex: 0">
            <Banner :options="banners" />
          </div>

          <div class="grid grid-cols-2 gap-2.5 pt-2">
            <div v-for="(val, i) in displayList" :key="val.id + i">
              <MiddleVideoCover
                v-if="val.mediaType"
                :title="val.title"
                :imgUrl="val.videoCover"
                :tags="val.tags"
                :views="val.viewTimes"
                :time="val.videoDuration"
                :vid="val.id"
                :type="val.type"
                :isWatched="val.isWatched"
              />
              <div v-else class="w-full h-full overflow-hidden rounded" @click="navigateHandle(val)">
                <ImgComponents :imgUrl="val.picUrl" object-fit="fill"></ImgComponents>
              </div>
            </div>
          </div>
        </van-list>

        <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />

      </van-pullRefresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useList, useNavigate } from "@/hooks";
import { uActions } from '@/api/user';
import { editedVideoForOneTheme } from "@/api/home";
import { useAppStore } from "@/store/app";
import { insertAds } from '@/utils';
import ThemeSort from "@/components/ThemeSort";
import EmptyPage from '@/components/empty/empty';
import Banner from "@/components/banner/banner.vue";
import MiddleVideoCover from "@/components/video/MiddleVideoCover";

const appStore = useAppStore();

const { navigateTo } = useNavigate();

const {
  query: { id, cid, title },
} = useRoute();

const banners = computed(() => appStore.adItem(11));;
const adCell = computed(() => appStore.adItem(1));

const implementationFetched = (list, allData) => {
  let nextAdPosition =
    Math.floor((allData.length - Math.floor(allData.length / 6)) / 5) * 6 + 5;
  let result = [];
  const ads = adCell.value?.adList || [];
  const len = ads.length;

  list.forEach((acc, index) => {
    // 插入元数据
    result.push(acc);
    if (allData.length + result.length === nextAdPosition) {
      // 插入广告
      const pushed = ads[((index + 1) / 5 - 1) % len];
      pushed && result.push(pushed);
      nextAdPosition += 6; // 更新下一个广告位置（每6个总数据插入一个广告）
    }
  });
  return result;
};


const navigateHandle = async ({ content, jumpType, id }) => {
  navigateTo(content, jumpType)
  await uActions({ actionType: 11, eventId: id })
}

const listParams = reactive({
  orderBy: "Newest",
  isDesc: false,
  themeId: parseInt(id),
  categoryId: parseInt(cid),
});

const {
  list,
  refreshing,
  loading,
  error,
  finished,
  finishedText,
  onRefresh,
  onLoad,
} = useList({
  serverHandle: editedVideoForOneTheme,
  immediateParams: listParams,
});


const displayList = computed(() => insertAds(list.value, adCell.value, 5));

</script>
