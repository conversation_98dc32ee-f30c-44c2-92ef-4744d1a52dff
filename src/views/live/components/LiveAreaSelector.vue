<template>
  <div class="px-4 py-3">
    <!-- Combined Selector Group -->
    <div class="flex items-center bg-[#292929] rounded-full px-4 py-2">
      <!-- Province Selector -->
      <div 
        class="flex items-center cursor-pointer flex-1 justify-between"
        @click="showProvinceSheet = true"
      >
        <div class="flex items-center">
          <img src="@/assets/webp/icon/icon-pin.webp" alt="" class="w-4 h-4 object-contain mr-2" />
          <span class="text-white text-sm">省份</span>
          <span class="text-white text-sm ml-2">{{ selectedProvince.name }}</span>
        </div>
        <img src="@/assets/webp/icon/icon-arrow.webp" alt="" class="w-3 h-3 object-contain ml-1 rotate-90">
      </div>

      <!-- Divider -->
      <div class="w-px h-4 bg-gray-500 mx-3"></div>

      <!-- City Selector -->
      <div 
        class="flex items-center cursor-pointer flex-1 justify-between"
        @click="showCitySheet = true"
      >
        <div class="flex items-center">
          <img src="@/assets/webp/icon/icon-pin.webp" alt="" class="w-4 h-4 object-contain mr-2" />
          <span class="text-white text-sm">城市</span>
          <span class="text-white text-sm ml-2">{{ selectedCity.name }}</span>
        </div>
        <img src="@/assets/webp/icon/icon-arrow.webp" alt="" class="w-3 h-3 object-contain ml-1 rotate-90">
      </div>
    </div>

    <!-- Province Selection Sheet -->
    <van-popup
      v-model:show="showProvinceSheet"
      :overlay="false"
      position="bottom"
      round
      safe-area-inset-bottom
      style="background-color: var(--van-black-500);"
    >
      <div class="flex flex-col h-[400px]" style="--van-search-background: transparent; --van-search-content-background: rgba(191, 191, 191, .15);">
        <h2 class="pt-6 pb-2 pl-7 text-white">选择省份</h2>
        <van-search
          style="--van-search-left-icon-color: #FFD631;"
          v-model="provinceSearchQuery"
          placeholder="搜索省份"
          shape="round"
          input-align="left"
        />

        <div class="area-list pl-4 pr-6 flex-1 overflow-auto">
          <van-cell 
            v-for="province in filteredProvinces" 
            :key="province.code"
            :title="province.name"
            clickable
            @click="selectProvince(province)"
            :style="getProvinceStyle(province.code)"
            class="mb-2"
          />
        </div>
        
        <!-- Action Buttons -->
        <div class="flex gap-4 p-4">
          <van-button 
            block 
            round
            color="#474126"
            style="color: #FFD631;"
            @click="cancelProvinceSelection"
            class="flex-1 text-white border-gray-600"
          >
            取消
          </van-button>
          <van-button 
            block 
            round
            type="primary"
            @click="confirmProvinceSelection"
            class="flex-1 bg-[var(--van-primary-color)] text-black border-[var(--van-primary-color)]"
          >
            确定
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- City Selection Sheet -->
    <van-popup
      v-model:show="showCitySheet"
      :overlay="false"
      position="bottom"
      round
      safe-area-inset-bottom
      style="background-color: var(--van-black-500);"
    >
      <div class="flex flex-col h-[400px]" style="--van-search-background: transparent; --van-search-content-background: rgba(191, 191, 191, .15);">
        <h2 class="pt-6 pb-2 pl-7 text-white">选择城市</h2>
        <van-search
          style="--van-search-left-icon-color: #FFD631;"
          v-model="citySearchQuery"
          placeholder="搜索城市"
          shape="round"
          input-align="left"
        />

        <div class="area-list pl-4 pr-6 flex-1 overflow-auto">
          <van-cell 
            v-for="city in filteredCities" 
            :key="city.code"
            :title="city.name"
            clickable
            @click="selectCity(city)"
            :style="getCityStyle(city.code)"
            class="mb-2"
          />
        </div>
        
        <!-- Action Buttons -->
        <div class="flex gap-4 p-4">
          <van-button 
            block 
            round
            color="#474126"
            style="color: #FFD631;"
            @click="cancelCitySelection"
            class="flex-1 text-white border-gray-600"
          >
            取消
          </van-button>
          <van-button 
            block 
            round
            type="primary"
            @click="confirmCitySelection"
            class="flex-1 bg-[var(--van-primary-color)] text-black border-[var(--van-primary-color)]"
          >
            确定
          </van-button>
        </div>
      </div>
    </van-popup>
  </div>
</template>

<script setup>
// Props & Emits
const emit = defineEmits(['province-change', 'city-change']);

const showProvinceSheet = ref(false);
const showCitySheet = ref(false);
const provinceSearchQuery = ref('');
const citySearchQuery = ref('');

// Demo data - replace with real data
const provinces = ref([
  { name: '江西', code: 'jiangxi' },
  { name: '北京', code: 'beijing' },
  { name: '上海', code: 'shanghai' },
  { name: '广东', code: 'guangdong' },
  { name: '浙江', code: 'zhejiang' },
  { name: '江苏', code: 'jiangsu' },
  { name: '山东', code: 'shandong' },
  { name: '河南', code: 'henan' },
  { name: '四川', code: 'sichuan' },
  { name: '湖北', code: 'hubei' },
]);

const cities = ref([
  { name: '南昌', code: 'nanchang', province: 'jiangxi' },
  { name: '九江', code: 'jiujiang', province: 'jiangxi' },
  { name: '赣州', code: 'ganzhou', province: 'jiangxi' },
  { name: '北京', code: 'beijing_city', province: 'beijing' },
  { name: '上海', code: 'shanghai_city', province: 'shanghai' },
  { name: '广州', code: 'guangzhou', province: 'guangdong' },
  { name: '深圳', code: 'shenzhen', province: 'guangdong' },
  { name: '杭州', code: 'hangzhou', province: 'zhejiang' },
  { name: '宁波', code: 'ningbo', province: 'zhejiang' },
]);

const selectedProvince = ref({ name: '江西', code: 'jiangxi' });
const selectedCity = ref({ name: '南昌', code: 'nanchang' });
const tempSelectedProvince = ref({ ...selectedProvince.value });
const tempSelectedCity = ref({ ...selectedCity.value });

const filteredProvinces = computed(() => {
  if (!provinceSearchQuery.value) return provinces.value;
  return provinces.value.filter(province =>
    province.name.toLowerCase().includes(provinceSearchQuery.value.toLowerCase())
  );
});

const filteredCities = computed(() => {
  let citiesForProvince = cities.value.filter(city => 
    city.province === selectedProvince.value.code
  );
  
  if (!citySearchQuery.value) return citiesForProvince;
  return citiesForProvince.filter(city =>
    city.name.toLowerCase().includes(citySearchQuery.value.toLowerCase())
  );
});

const selectProvince = (province) => {
  tempSelectedProvince.value = { ...province };
};

const selectCity = (city) => {
  tempSelectedCity.value = { ...city };
};

const confirmProvinceSelection = () => {
  selectedProvince.value = { ...tempSelectedProvince.value };
  emit('province-change', selectedProvince.value);
  showProvinceSheet.value = false;
  
  // Reset city if province changed
  const citiesForNewProvince = cities.value.filter(city => 
    city.province === selectedProvince.value.code
  );
  if (citiesForNewProvince.length > 0) {
    selectedCity.value = citiesForNewProvince[0];
    tempSelectedCity.value = { ...selectedCity.value };
  }
};

const confirmCitySelection = () => {
  selectedCity.value = { ...tempSelectedCity.value };
  emit('city-change', selectedCity.value);
  showCitySheet.value = false;
};

const cancelProvinceSelection = () => {
  tempSelectedProvince.value = { ...selectedProvince.value };
  showProvinceSheet.value = false;
};

const cancelCitySelection = () => {
  tempSelectedCity.value = { ...selectedCity.value };
  showCitySheet.value = false;
};

const getProvinceStyle = (code) => {
  return tempSelectedProvince.value.code === code ? {
    backgroundColor: '#403B24',
    color: '#fff',
    borderRadius: '40px',
    border: '1px solid #ffd631'
  } : {
    backgroundColor: '#1F1F1F',
    color: '#fff',
    borderWidth: '1px solid transparent',
  };
};

const getCityStyle = (code) => {
  return tempSelectedCity.value.code === code ? {
    backgroundColor: '#403B24',
    color: '#fff',
    borderRadius: '40px',
    border: '1px solid #ffd631'
  } : {
    backgroundColor: '#1F1F1F',
    color: '#fff',
    borderWidth: '1px solid transparent',
  };
};
</script>

<style scoped>
.area-list .van-cell {
  border-radius: 40px;
  margin-bottom: 8px;
}
.area-list .van-cell:after {
  display: none;
}
</style> 