<template>
  <div class="px-4 py-3">
    <swiper
      :slides-per-view="4.5"
      :space-between="12"
      :grab-cursor="true"
      class="avatar-swiper"
    >
      <swiper-slide v-for="(avatar, index) in avatarList" :key="index">
        <div class="flex flex-col items-center">
          <div class="relative">
            <div class="w-16 h-16 rounded-full border-2 border-solid border-[#ffd631]">
              <img 
                :src="avatar.image" 
                :alt="avatar.name"
                class="w-full h-full rounded-full object-cover"
              />
            </div>
            <div class="absolute bottom-[-14px] left-0 right-0 flex justify-center">
              <span class="bg-black rounded px-2 py-1 text-xs text-[#ffd631] text-ellipsis overflow-hidden whitespace-nowrap">
                {{ avatar.category }}
              </span>
            </div>
          </div>
          <div class="mt-4 text-xs text-white text-center max-w-16 truncate">
            {{ avatar.name }}
          </div>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script setup>
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';

defineProps({
  avatarList: {
    type: Array,
    default: () => []
  }
});
</script>

<style lang="less" scoped>
.avatar-swiper {
  :deep(.swiper-slide) {
    width: auto !important;
  }
}
</style> 