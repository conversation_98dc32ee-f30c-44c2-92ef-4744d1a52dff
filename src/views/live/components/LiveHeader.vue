<template>
  <div class="flex items-center justify-between z-100 h-12 flex-none px-3 bg-[#292929]">
    <div class="flex-1 relative z-1 w-full h-[var(--van-tabs-line-height)] overflow-hidden"
      style="--van-tabs-nav-background: transparent;">
      <div class="absolute top-0 right-0 bottom-0 w-10 bg-gradient-to-l from-[#292929] to-transparent z-10"></div>
      <van-tabs v-if="tabList.length > 0" :ellipsis="false" :line-height="0">
        <van-tab v-for="val in tabList" :key="val.id" :title="val.name" :name="val.key" />
      </van-tabs>
    </div>
    <div class="flex">
      <div class="w-5 h-5 ml-2 cursor-pointer" @click="handleSearchClick">
        <img class="w-full h-full" src="@/assets/live/<EMAIL>" />
      </div>
      <div class="w-5 h-5 ml-2">
        <img class="w-full h-full" src="@/assets/live/<EMAIL>" />
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  tabList: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['search-click']);

const handleSearchClick = () => {
  emit('search-click');
};
</script>

<style lang="less" scoped>
:deep(.van-tab--grow) {
  padding: 0 8px;
}
:deep(.van-tab--active) {
  font-size: 16px;
  color: #fff;
}
:deep(.van-tab) {
  color: rgba(255, 255, 255, 0.7);
}
</style> 