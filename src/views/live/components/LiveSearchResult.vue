<template>
  <div class="h-full bg-[#181818] flex flex-col">
    <!-- Search Header -->
    <div class="flex items-center px-4 py-3 bg-[#181818] relative z-10">
      <div class="flex-1 relative">
        <div class="flex items-center bg-[#2A2A2A] rounded-full px-4 py-3">
          <!-- Search Icon Placeholder -->
          <img src="@/assets/live/<EMAIL>" alt="" class="w-4 h-4 object-contain mr-3" />
          <input 
            v-model="searchQuery"
            type="text" 
            placeholder="请输入要搜索的昵称或ID"
            class="flex-1 bg-transparent text-white text-base outline-none border-none placeholder-gray-500"
            @keyup.enter="performSearch"
          />
        </div>
      </div>
      <button 
        @click.stop="closeSearch"
        class="ml-4 text-white text-base cursor-pointer select-none bg-transparent border-none hover:opacity-80 transition-opacity min-w-[40px] py-2 px-2 flex items-center justify-center"
        type="button"
      >
        取消
      </button>
    </div>

    <!-- Search Results -->
    <div class="flex-1 overflow-y-auto px-3">
      <!-- Empty State (when not in search mode) -->
      <div v-if="route.query.q === undefined" class="flex flex-col items-center justify-center h-64">
        <!-- Emoji Placeholder -->
        <div class="w-16 h-16 mb-6 rounded-full flex items-center justify-center">
          <img src="@/assets/live/icon-emo.png" alt="" class="w-full h-full object-contain" />
        </div>
        <p class="text-gray-400 text-base text-center">很抱歉，未匹配到主播！</p>
      </div>

      <!-- Search Results Found -->
      <div v-else-if="searchResults.length > 0">
        <!-- Results Header -->
        <div class="flex items-center py-4">
          <div class="flex items-center">
            <!-- Thumb Icon Placeholder -->
            <img src="@/assets/live/icon-thumb.png" alt="" class="w-8 h-8 mr-3 object-contain" />
            <span class="text-white text-base">已为您搜索到 {{ searchResults.length }} 个美女主播</span>
          </div>
        </div>

        <!-- Direct Results Grid (no recommendation header) -->
        <div class="grid grid-cols-2 gap-3 pb-4">
          <LiveRoomCard 
            v-for="(item, idx) in searchResults" 
            :key="idx" 
            :avatar="item.avatar" 
            :title="item.title"
            :desc="item.desc" 
            :tags="item.tags" 
            :iconPk="item.iconPk" 
            :iconPhone="item.iconPhone"
            :hostName="item.hostName"
            :viewerCount="item.viewerCount"
          />
        </div>
      </div>

      <!-- No Results - Show Recommendations -->
      <div v-else>
        <!-- No Results Message -->
        <div class="flex flex-col items-center justify-center h-40">
          <!-- Emoji Placeholder -->
          <div class="w-16 h-16 mb-6 rounded-full flex items-center justify-center">
            <img src="@/assets/live/icon-emo.png" alt="" class="w-full h-full object-contain" />
          </div>
          <p class="text-gray-400 text-base text-center">很抱歉，未匹配到主播！</p>
        </div>

        <!-- Recommendations Section -->
        <div class="mt-6">
          <!-- Recommendations Header -->
          <div class="flex items-center justify-between mb-4">
            <div class="flex items-center">
              <!-- Fire Icon Placeholder -->
              <img src="@/assets/live/icon-fire-2.png" alt="" class="w-8 h-8 mr-3 object-contain" />
              <span class="text-white font-medium text-base">为你推荐</span>
            </div>
            <div class="flex items-center bg-[#2A2A2A] rounded-full px-4 py-2">
              <span class="text-white text-sm mr-2">换一批</span>
              <!-- Arrow Icon Placeholder -->
              <img src="@/assets/live/icon-switch.png" alt="" class="w-4 h-4 object-contain" />
            </div>
          </div>

          <!-- Recommendations Grid -->
          <div class="grid grid-cols-2 gap-3 pb-4">
            <LiveRoomCard 
              v-for="(item, idx) in allResults.slice(0, 4)" 
              :key="`rec-${idx}`" 
              :avatar="item.avatar" 
              :title="item.title"
              :desc="item.desc" 
              :tags="item.tags" 
              :iconPk="item.iconPk" 
              :iconPhone="item.iconPhone"
              :hostName="item.hostName"
              :viewerCount="item.viewerCount"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import LiveRoomCard from '@/views/live_broadcast/components/LiveRoomCard.vue';

const emit = defineEmits(['close']);

const route = useRoute();
const router = useRouter();

const searchQuery = ref(route.query.q || '');

watch(() => route.query.q, (newQuery) => {
  if (newQuery !== undefined) {
    searchQuery.value = newQuery || '';
  }
}, { immediate: true });

const updateSearchQuery = (query) => {
  router.push({
    path: route.path,
    query: { ...route.query, q: query }
  });
};

const performSearch = () => {
  updateSearchQuery(searchQuery.value);
};

watch(searchQuery, (newValue) => {
  if (route.query.q !== undefined) {
    updateSearchQuery(newValue);
  }
});

const closeSearch = () => {
  const { q, ...restQuery } = route.query;
  router.push({ 
    path: route.path, 
    query: restQuery 
  });
  emit('close');
};

// Demo search results data
const allResults = ref([
  {
    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '全能女射手，可教学~',
    tags: [{ text: '广州' }, { text: '按时收费' }],
    iconPk: true,
    iconPhone: true,
    hostName: '电竞玉兔',
    viewerCount: '76.8万',
  },
  {
    avatar: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '香港时时彩',
    desc: '可盐可甜的辅助小萝莉~',
    tags: [{ text: '上海' }, { text: 'PK' }],
    iconPk: true,
    iconPhone: true,
    hostName: '小纯御',
    viewerCount: '76.8万',
  },
  {
    avatar: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '谁喜欢酷姐姐？来看看！',
    tags: [{ text: '深圳' }],
    iconPk: true,
    iconPhone: true,
    hostName: '至纯',
    viewerCount: '76.8万',
  },
  {
    avatar: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '香港时时彩',
    desc: '甜心数学在线清理吧~',
    tags: [{ text: '北京' }],
    iconPk: true,
    iconPhone: true,
    hostName: '甜心',
    viewerCount: '76.8万',
  },
  {
    avatar: 'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '英雄联盟魔法猫咪五杀！',
    tags: [{ text: '重庆' }],
    iconPk: true,
    iconPhone: true,
    hostName: '小美兰',
    viewerCount: '76.8万',
  },
  {
    avatar: 'https://images.unsplash.com/photo-1519340333755-c1aa5571fd46?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '香港时时彩',
    desc: '记录生活美好与你分享~',
    tags: [{ text: '安徽不允' }],
    iconPk: true,
    iconPhone: true,
    hostName: '玉冷冷',
    viewerCount: '76.8万',
  }
]);

const searchResults = computed(() => {
  if (route.query.q === undefined) return [];
  
  if (!searchQuery.value.trim()) {
    return allResults.value;
  }
  
  return allResults.value.filter(item => 
    item.hostName.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    item.desc.toLowerCase().includes(searchQuery.value.toLowerCase())
  );
});

</script>
