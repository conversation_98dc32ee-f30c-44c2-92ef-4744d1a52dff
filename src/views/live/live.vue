<template>
  <PageLayout>
    <div class="h-full bg-[#181818]">
      <!-- Search Result View -->
      <LiveSearchResult 
        v-if="isSearchMode"
        @close="closeSearch"
      />
      
      <!-- Main Live View -->
      <div v-else class="flex flex-col overflow-hidden h-full overflow-scrolling">
        <!-- Header with tabs -->
        <LiveHeader :tab-list="tabList" @search-click="openSearch" />

        <!-- Select area -->
        <LiveAreaSelector @province-change="handleProvinceChange" @city-change="handleCityChange" />

        <!-- Content area -->
        <div class="flex-1 overflow-y-auto">
          <!-- Avatar swiper -->
          <AvatarSwiper :avatar-list="avatarList" />

          <div class="grid grid-cols-2 gap-3 px-3 pb-4">
            <LiveRoomCard 
              v-for="(item, idx) in demoList" 
              :key="idx" 
              :avatar="item.avatar" 
              :title="item.title"
              :desc="item.desc" 
              :tags="item.tags" 
              :iconPk="item.iconPk" 
              :iconPhone="item.iconPhone"
              :hostName="item.hostName"
              :viewerCount="item.viewerCount"
            />
          </div>
        </div>
      </div>
    </div>
  </PageLayout>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAppStore } from '@/store/app';
import LiveHeader from './components/LiveHeader.vue';
import AvatarSwiper from './components/AvatarSwiper.vue';
import LiveAreaSelector from './components/LiveAreaSelector.vue';
import LiveSearchResult from './components/LiveSearchResult.vue';
import LiveRoomCard from '@/views/live_broadcast/components/LiveRoomCard.vue';

defineOptions({
  name: 'Live'
});

const router = useRouter();
const route = useRoute();
const appStore = useAppStore();

// Search mode based on query params
const isSearchMode = computed(() => {
  return route.query.hasOwnProperty('q');
});

// Navigation methods
const openSearch = () => {
  router.push({ 
    path: route.path, 
    query: { ...route.query, q: '小' } 
  });
};

const closeSearch = () => {
  console.log('Parent closeSearch called'); // Debug log
  const { q, ...restQuery } = route.query;
  router.push({ 
    path: route.path, 
    query: restQuery 
  });
};

const tabList = ref([
  { id: 1, name: '关注', key: '0' },
  { id: 2, name: '推荐', key: '1' },
  { id: 3, name: 'PK', key: '2' },
  { id: 4, name: '游戏', key: '3' },
  { id: 5, name: '跳蛋', key: '4' },
  { id: 6, name: '附近', key: '5' },
  { id: 7, name: '附近', key: '6' },
  { id: 8, name: '附近', key: '7' },
  { id: 9, name: '附近', key: '8' },
]);

const tabIndex = ref('0');

const avatarList = ref([
  {
    name: 'Miss鱼儿sdsd',
    category: '柳州',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '御姐热舞...',
    category: '齐齐哈尔',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '贵阳',
    category: '贵阳',
    image: 'https://images.unsplash.com/photo-1550525811-e5869dd03032?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '苏州',
    category: '苏州',
    image: 'https://images.unsplash.com/photo-1500648762-4d5797f040d2?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '成都',
    category: '成都',
    image: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ab?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '深圳',
    category: '深圳',
    image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '北京',
    category: '北京',
    image: 'https://images.unsplash.com/photo-1463453091185-61582044d556?auto=format&fit=facearea&w=256&h=256&q=80'
  }
]);

const demoList = ref([
  {
    avatar:
      'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '全能女射手，可教学~',
    tags: [{ text: '广州' }, { text: '按时收费' }],
    iconPk: true,
    iconPhone: true,
    hostName: '小纯御',
    viewerCount: '76.8万',
  },
  {
    avatar:
      'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '健气双胞姐妹花！来撩~',
    tags: [{ text: '深圳' }, { text: '新手' }],
    iconPk: true,
    iconPhone: true,
    hostName: '甜心',
    viewerCount: '45.2万',
  },
  {
    avatar:
      'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '英雄联盟法妹颜值天花板！',
    tags: [{ text: '上海' }, { text: '高分段' }],
    iconPk: true,
    iconPhone: true,
    hostName: '游戏女神',
    viewerCount: '92.1万',
  },
  {
    avatar:
      'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '猜时间的彩',
    desc: '可爱可爱的魅力小萝莉~',
    tags: [{ text: '北京' }, { text: '萌妹' }],
    iconPk: true,
    iconPhone: true,
    hostName: '萌萌',
    viewerCount: '31.5万',
  },
  {
    avatar:
      'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '猜时间的彩',
    desc: '都快要在美颜崩盘~',
    tags: [{ text: '成都' }, { text: '高颜值' }],
    iconPk: true,
    iconPhone: true,
    hostName: '美美',
    viewerCount: '68.3万',
  },
  {
    avatar:
      'https://images.unsplash.com/photo-1519340333755-c1aa5571fd46?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '记录生活美与分享~',
    tags: [{ text: '重庆' }, { text: '才艺' }],
    iconPk: true,
    iconPhone: true,
    hostName: '才艺达人',
    viewerCount: '54.7万',
  },
  {
    avatar:
      'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '甜美声线，互动多多',
    tags: [{ text: '杭州' }, { text: '甜妹' }],
    iconPk: true,
    iconPhone: true,
    hostName: '甜美小姐姐',
    viewerCount: '76.8万',
  },
  {
    avatar:
      'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '元气少女，活力满满',
    tags: [{ text: '苏州' }, { text: '元气' }],
    iconPk: true,
    iconPhone: true,
    hostName: '元气少女',
    viewerCount: '83.9万',
  },
  {
    avatar:
      'https://images.unsplash.com/photo-1519985176271-adb1088fa94c?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '高冷御姐，气场全开',
    tags: [{ text: '天津' }, { text: '御姐' }],
    iconPk: true,
    iconPhone: true,
    hostName: '御姐范',
    viewerCount: '97.2万',
  },
  {
    avatar:
      'https://images.unsplash.com/photo-1463453091185-61582044d556?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '文艺青年，诗和远方',
    tags: [{ text: '厦门' }, { text: '文艺' }],
    iconPk: true,
    iconPhone: true,
    hostName: '文艺女青年',
    viewerCount: '42.6万',
  },
]);

// Event handlers for area selection
const handleProvinceChange = (province) => {
  console.log('Province changed:', province);
  // Handle province change - could filter live rooms by province
};

const handleCityChange = (city) => {
  console.log('City changed:', city);
  // Handle city change - could filter live rooms by city
};
</script>

<style lang="less" scoped>
.home-container {
  // Component-specific styles can be moved to individual components
}
</style>