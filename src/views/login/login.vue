<template>
  <SecondPageLayout :clickLeftHandle="leftHandle">
    <div class="login_logo mx-auto w-[196px] h-[55.75px] mt-12">
      <ImgComponents :imgUrl="merchantLogo?.bind_phone_logo" />
    </div>

    <CountrySelector v-model="selectedCountry" @change="onCountryChange" />

    <div class="login_form w-11/12 mx-auto">
      <van-form
        ref="loginForm"
        :show-error-message="false"
      >
        <div class="flex items-stretch h-[43px] mb-6 rounded-full bg-[var(--van-black-500)] overflow-hidden">
          <div class="flex items-center justify-center w-[90px] text-[13px] text-[var(--van-primary-color)]">
            <span>{{ `+${selectedCountry.code}` }}</span>
          </div>
          <div class="flex-1">
            <van-field
              v-model="params.tel"
              name="tel"
              type="tel"
              autocomplete="off"
              placeholder="请输入手机号码"
              label-align="top"
              :rules="[{ required: true, message: '请输入手机号码' }]"
            >
            </van-field>
          </div>
          <div
            class="flex items-center justify-center w-[100px] text-[13px] rounded-r-full text-black bg-[var(--van-primary-color)]"
            @click.stop="getCaption"
          >
            <span v-if="isActive">还剩{{ t }}秒</span>
            <span v-else>获取验证码</span>
          </div>
        </div>
        <div class="flex items-stretch h-[43px] mb-6 rounded-full bg-[var(--van-black-500)] overflow-hidden">
          <div class="flex items-center justify-center w-[90px] text-[13px] text-[var(--van-primary-color)]">
            <span>验证码</span>
          </div>
          <div class="flex-1">
            <van-field
              v-model="params.code"
              name="code"
              autocomplete="off"
              placeholder="请输入验证码"
              label-align="top"
              :rules="[{ required: true, message: '请输入验证码' }]"
            >
            </van-field>
          </div>
        </div>
      </van-form>
      <div class="login_btn mt-12">
        <van-button type="primary" block round @click="onSubmit">登录</van-button>
      </div>
      <div class="flex justify-center mt-4 text-sm text-[var(--van-gray-6)]">
        <span class="cursor-pointer" @click="goToAccountLogin">账号密码登录</span>
      </div>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useAppStore } from '@/store/app';
import { useLogin, useCaption } from '@/hooks';
import { PageEnum } from '@/enums/pageEnum';
import CountrySelector from '@/components/CountrySelector/index.vue';
import { LoginTypeEnum } from '@/enums/loginTypeEnum';

const appStore = useAppStore();
const merchantLogo = computed(() => appStore.appConfig?.merchantLogo);

const router = useRouter();
const loginForm = ref(null);
const params = reactive({
  tel: '',
  code: ''
});

const selectedCountry = ref({ name: '中国', code: 86 });

const onCountryChange = (country) => {
  console.log('Country changed to:', country);
};

const leftHandle = () => {
  router.replace({ path: PageEnum.BASE_HOME });
};

const goToAccountLogin = () => {
  router.replace({ path: '/account-login' });
};

const { t, isActive, isPending, getCaption } = useCaption(loginForm, () => ({ tel: params.tel, pre: `+${selectedCountry.value.code}`, codeType: 'Login' }));

const onSubmit = () => {
  loginForm.value.validate('tel').then(() => {
    if (isPending.value === false) {
      showToast('请先发送验证码');
      return;
    }
    loginForm.value.validate('code').then(async () => {
      const { isFinished } = await useLogin({
        type: LoginTypeEnum.PHONE_LOGIN,
        tel: params.tel,
        pre: selectedCountry.value.code,
        code: params.code,
      });

      if (isFinished.value) {
        setTimeout(() => {
          leftHandle();
          sessionStorage.removeItem('HOME_POPUP');
          sessionStorage.removeItem('GAME_POPUP');
        }, 500);
      }
    }).catch((err) => {
      showToast(err.message);
    });
  }).catch((err) => {
    showToast(err.message);
  });
};
</script>
