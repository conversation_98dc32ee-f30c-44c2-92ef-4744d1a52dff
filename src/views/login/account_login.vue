<template>
  <SecondPageLayout :clickLeftHandle="leftHandle">
    <div class="login_logo mx-auto w-[196px] h-[55.75px] mt-12">
      <ImgComponents :imgUrl="merchantLogo?.bind_phone_logo" />
    </div>
    
    <div class="login_form w-11/12 mx-auto mt-10">
      <div class="mb-6 text-white text-lg font-bold">
        账号密码登录
      </div>
      <van-form
        ref="loginForm"
        :show-error-message="false"
      >
        <div class="flex items-stretch h-[43px] mb-6 rounded-full bg-[var(--van-black-500)] overflow-hidden">
          <div class="flex items-center justify-center w-[90px] text-[13px] text-[var(--van-primary-color)]">
            <span>账号</span>
          </div>
          <div class="flex-1">
            <van-field
              v-model="params.account"
              name="account"
              autocomplete="off"
              placeholder="请输入您的账号"
              label-align="top"
              :rules="[{ required: true, message: '请输入您的账号' }]"
            >
            </van-field>
          </div>
        </div>

        <div class="flex items-stretch h-[43px] mb-6 rounded-full bg-[var(--van-black-500)] overflow-hidden">
          <div class="flex items-center justify-center w-[90px] text-[13px] text-[var(--van-primary-color)]">
            <span>密码</span>
          </div>
          <div class="flex-1">
            <van-field
              v-model="params.password"
              name="password"
              type="password"
              autocomplete="off"
              placeholder="请输入您的密码"
              label-align="top"
              :rules="[{ required: true, message: '请输入您的密码' }]"
            >
            </van-field>
          </div>
        </div>
      </van-form>
      
      <div class="login_btn mt-12">
        <van-button type="primary" block round @click="onSubmit" class="h-12 bg-[var(--van-primary-color)] border-[var(--van-primary-color)]">
          <span class="text-black font-bold">登录</span>
        </van-button>
      </div>
      
      <div class="flex justify-center mt-4 text-sm text-[var(--van-gray-6)]">
        <span class="cursor-pointer" @click="goToPhoneLogin">手机验证码登录</span>
      </div>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useAppStore } from '@/store/app';
import { PageEnum } from '@/enums/pageEnum';
import { useLogin } from '@/hooks';
import { LoginTypeEnum } from '@/enums/loginTypeEnum';

const appStore = useAppStore();
const merchantLogo = computed(() => appStore.appConfig?.merchantLogo);

const router = useRouter();
const loginForm = ref(null);
const params = reactive({
  account: '',
  password: ''
});

const leftHandle = () => {
  router.replace({ path: PageEnum.BASE_HOME });
};

const goToPhoneLogin = () => {
  router.replace({ path: '/login' });
};

const onSubmit = () => {
  loginForm.value.validate(['account', 'password']).then(async () => {
    const { isFinished, isError, errorMessage } = await useLogin({
      type: LoginTypeEnum.ACCOUNT_LOGIN,
      ac: params.account,
      password: params.password
    });

    if (isFinished.value) {
      setTimeout(() => {
        leftHandle();
        sessionStorage.removeItem('HOME_POPUP');
        sessionStorage.removeItem('GAME_POPUP');
      }, 500);
    }
  }).catch((err) => {
    showToast(err.message);
  });
};

</script>
