<template>
  <SecondPageLayout @click-right="clickRightHandle">
    <template #navbar-right>
      <img src="@/assets/icons/qs.png" class="w-18px h-18px" />
    </template>
    <div class="w-[345px] mx-auto mt-3 box-border flex items-center justify-center h-[65px] rounded bg-[var(--van-black-500)]">
      <div class="flex-1 flex items-center justify-around text-15px">
        <div class="text-center">
          <p>可领取</p>
          <p class="mt-1 text-[var(--van-primary-color)]">{{ claim.restClaim }}</p>
        </div>
        <div class="text-center">
          <p>累计领取</p>
          <p class="mt-1 text-[var(--van-primary-color)]">{{ claim.alreadyClaim }}</p>
        </div>
      </div>
      <div class="w-84px mr-2.5" style="--van-button-small-height: 30px">
        <van-button type="primary" size="small" block @click="ClaimAllHandle">一键领取</van-button>
      </div>
    </div>

    <div class="w-[345px] my-4 mx-auto grid grid-cols-4 gap-3">
      <div
        v-for="val in regionTypeMaps"
        :key="val.value"
        :class="[
          'h-8 leading-8 text-center rounded text-xs',
          {
            'text-white bg-[var(--van-black-500)]': val.value !== params.regionType,
            'text-[var(--van-black-500)] bg-[var(--van-primary-color)]': val.value === params.regionType
          }
        ]"
        @click="changeHandle(val)"
      >
        <span>{{ val.label }}</span>
      </div>
    </div>

    <div class="w-[345px] mx-auto">
      <van-pullRefresh v-model="refreshing" @refresh="onRefresh" class="min-h-full">
        <van-list v-model:loading="loading" v-model:error="error" :finished="finished" @load="onLoad">
          <div
            v-for="val in list"
            :key="val.day"
            class="flex items-center justify-between h-[65px] px-4 py-2 mb-4 box-border bg-[var(--van-black-500)]"
          >
            <div class="flex flex-col h-full box-border justify-between">
              <p class="text-15px">
                返水 <span class="pl-5 text-[var(--van-primary-color)]">+{{ val.amount }}</span>
              </p>
              <span class="text-xs">{{ val.day }}</span>
            </div>
            <div class="flex flex-col items-end h-full justify-between">
              <van-button v-if="val.status === 'Done'" color="#c0c0c0" round size="mini" class="w-16" style="color: #2f2f2f"
                >已领取</van-button
              >
              <van-button v-else type="primary" round size="mini" class="w-16" @click="ClaimDayHandle(val)">待领取</van-button>
              <span v-if="val.status === 'Done'" class="text-xs">{{ val.claimTime }}</span>
            </div>
          </div>
        </van-list>
        <EmptyPage v-if="!loading && list.length <= 0" text="暂无数据" />
      </van-pullRefresh>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { TotalClaimInfoAPI, TimeRegionClaimInfoAPI, ClaimOneDayAPI, ClaimAllAPI } from '@/api/promotion';
import EmptyPage from '@/components/empty/empty';

const router = useRouter();

const regionTypeMaps = [
  { label: '今日', value: 'CurrentDay' },
  { label: '三天内', value: 'ThreeDay' },
  { label: '七日内', value: 'OneWeek' },
  { label: '一个月内', value: 'OneMonth' }
];

const claim = reactive({
  alreadyClaim: 0,
  restClaim: 0
});

const params = reactive({
  regionType: 'CurrentDay',
  pageFilterId: ''
});

const refreshing = ref(false);
const loading = ref(false);
const error = ref(false);
const finished = ref(false);
const list = ref([]);


const clickRightHandle = () => {
  router.push({ path: '/rebate_detail' })
}

const getTotalClaim = async () => {
  try {
    const { data } = await TotalClaimInfoAPI();
    claim.alreadyClaim = data.alreadyClaim;
    claim.restClaim = data.restClaim;
  } catch (e) {
    //
  }
};

const ClaimAllHandle = async () => {
  try {
    const {
      data: { isSuccess }
    } = await ClaimAllAPI();
    onRefresh()
    if (isSuccess) showSuccessToast('全部领取成功');
  } catch (e) {
    //
  }
};

const ClaimDayHandle = async ({ day }) => {
  try {
    const {
      data: { isSuccess }
    } = await ClaimOneDayAPI({ day });
    getTotalClaim();
    onRefresh();
    if (isSuccess) showSuccessToast('领取成功');
  } catch (e) {
    //
  }
};

const onRefresh = () => {
  finished.value = false;
  loading.value = true;
  params.pageFilterId = '';
  onLoad();
};

const onLoad = async () => {
  try {
    const { data } = await TimeRegionClaimInfoAPI(params);
    refreshing.value = false;
    loading.value = false;

    const res = data.items || [];
    if (params.pageFilterId === '') {
      list.value = res || [];
    } else {
      list.value = list.value.concat(res);
    }
    if (res.length === 0) {
      finished.value = true;
    } else {
      finished.value = false;
      params.pageFilterId = data.pageFilterId;
    }

    finished.value = true;
  } catch (e) {
    //
  }
};

const changeHandle = ({ value }) => {
  list.value = [];
  params.regionType = value;
  onRefresh();
};

onMounted(() => {
  getTotalClaim();
});
</script>
