<template>
  <div class="absolute top-0 left-0 w-full h-full">
    <img src="@/assets/live/bg_01.jpg" class="w-full h-full object-cover" />
  </div>

  <LiveHeader
    :game-id="gameId"
    :game-result="gameResult"
    :game-animated="gameAnimated"
    @open-profile="openProfile"
    @open-leaderboard="openLeaderboard"
    @open-watch-list="openWatchList"
    @open-vip-members="openVipMembers"
  />

  <GiftSection />

  <div class="fixed left-0 bottom-0 right-0">
    <LiveComments />
    <template v-if="openningBottomItem === 'commentInput' || openningBottomItem === ''">
      <RightActionBar
        v-if="openningBottomItem !== 'commentInput'"
        v-model:comment-text="commentText"
        @icon-action="handleIconAction"
        @vip-charge="handleVipCharge"
        @settings="handleSettings"
      />
      <BottomActionBar
        v-if="openningBottomItem !== 'commentInput'"
        v-model:comment-text="commentText"
        @icon-action="handleIconAction"
        @vip-charge="handleVipCharge"
        @settings="handleSettings"
        @comment-input="handleCommentInput"
      />
      <InputComment v-if="openningBottomItem === 'commentInput'" />
    </template>
    <div v-if="openningBottomItem === 'bottomPopup'" class="mt-4">
      <BottomContent @close="openningBottomItem = ''" :active-tool="bottomPopupActiveTool" @change-tool="handleToolChange" />
    </div>
  </div>

  <div class="fixed left-0 bottom-[350px] right-0 pointer-events-none">
    <div class="px-4">
      <WinningPopup />
    </div>
    <div class="mt-[20px] px-2">
      <NewUserJoin />
      <BetSignalContent />
      <GiftNotice />
    </div>
    <div class="mt-[100px]">
      <HostInfo />
    </div>
  </div>

  <FloatingTimer :live-timer="liveTimer" />

  <!-- Slide -->
  <div class="fixed top-0 right-0 max-w-[150px] h-full flex-col items-end z-30 hidden">
    <div class="h-[100vh] py-2.5 px-[19px] overflow-y-auto space-y-2.5 bg-black/75">
      <LiveRoomCard
        v-for="(item, idx) in demoList"
        :key="idx"
        :avatar="item.avatar"
        :title="item.title"
        :desc="item.desc"
        :tags="item.tags"
        :iconPk="item.iconPk"
        :iconPhone="item.iconPhone"
      />
    </div>
  </div>

  <!-- Action Sheets -->
  <ProfileActionSheet v-model:show="showProfileSheet" />
  <LeaderboardActionSheet v-model:show="showLeaderboardSheet" />
  <WatchListActionSheet v-model:show="showWatchListSheet" />
  <GuardianActionSheet v-if="showVipMembersSheet" v-model:show="showVipMembersSheet" />
  <SettingsActionSheet v-model:show="showSettingsSheet" />
</template>

<script setup>
import { showToast } from 'vant';
import { onMounted, onUnmounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';

// Components
import { useActionSheets } from '@/hooks/useActionSheets.js';
import BottomActionBar from './components/BottomActionBar.vue';
import BottomContent from './components/BottomContent.vue';
import FloatingTimer from './components/FloatingTimer.vue';
import GiftSection from './components/GiftSection.vue';
import HostInfo from './components/HostInfo.vue';
import InputComment from './components/InputComment.vue';
import LeaderboardActionSheet from './components/LeaderboardActionSheet.vue';
import LiveComments from './components/LiveComments.vue';
import LiveHeader from './components/LiveHeader.vue';
import VipMembersActionSheet from './components/VipMembersActionSheet.vue';
import NewUserJoin from './components/NewUserJoin.vue';
import ProfileActionSheet from './components/ProfileActionSheet.vue';
import RightActionBar from './components/RightActionBar.vue';
import SettingsActionSheet from './components/SettingsActionSheet.vue';
import WatchListActionSheet from './components/WatchListActionSheet.vue';
import WinningPopup from './components/WinningPopup.vue';
import BetSignalContent from './components/BetSignalContent.vue';
import GiftNotice from './components/GiftNotice.vue';
import GuardianActionSheet from './components/GuardianActionSheet.vue';

const router = useRouter();
const route = useRoute();

// Use the action sheets hook
const {
  showProfileSheet,
  showLeaderboardSheet,
  showWatchListSheet,
  showVipMembersSheet,
  showSettingsSheet,
  openProfile,
  openLeaderboard,
  openWatchList,
  openVipMembers,
  openSettings
} = useActionSheets();

// Component state
const commentText = ref('');
const liveTimer = ref('02:30');
const streamerTitle = ref('季大哥');
const openningBottomItem = ref('');
const bottomPopupActiveTool = ref('game');

// TinsLottery game state
const gameId = ref('1');
const gameResult = ref('123');
const gameAnimated = ref(true);

// Host info
const hostInfo = ref({
  name: '小纯御',
  avatar: '/api/placeholder/40/40',
  status: '关注主播不迷路'
});

// Live comments data
const liveComments = ref([
  {
    id: '4',
    type: 'notice',
    data: ['<span class="text-[#ffd631]">发财大世豪</span> 在一分钟一口气进了'],
    timestamp: Date.now()
  },
  {
    id: '1',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '小纯御御姐 送出【神话一下】×1',
        user_info: {
          nickname: '系统',
          level: 10,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  {
    id: '2',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '小可爱 关注了小纯御',
        user_info: {
          nickname: '系统',
          level: 1,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  {
    id: '3',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '发财大世豪 在一分钟一口气进了',
        user_info: {
          nickname: '系统',
          level: 20,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  {
    id: '5',
    type: 'winning',
    data: {
      client_id: 'Game',
      data: {
        anchor_id: '2727',
        code: 0,
        data: {
          game_name: '一分快三',
          money: '10.00',
          target: '2727',
          user_id: 2809115,
          user_name: '发财大世豪'
        },
        game_id: 1,
        game_type: 'WIN_MESSAGE',
        liveId: '297703',
        msg: 'success',
        target: '2727'
      },
      event_type: 6,
      uuid: '1934600597685751808'
    },
    timestamp: Date.now()
  }
]);

// Demo list
const demoList = ref([
  {
    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '全能女射手，可教学~',
    tags: [{ text: '广州' }, { text: '按时收费' }],
    iconPk: true,
    iconPhone: true
  },
  {
    avatar: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '健气双胞姐妹花！来撩~',
    tags: [{ text: '深圳' }, { text: '新手' }],
    iconPk: true,
    iconPhone: true
  },
  {
    avatar: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '英雄联盟法妹颜值天花板！',
    tags: [{ text: '上海' }, { text: '高分段' }],
    iconPk: true,
    iconPhone: true
  },
  {
    avatar: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '猜时间的彩',
    desc: '可爱可爱的魅力小萝莉~',
    tags: [{ text: '北京' }, { text: '萌妹' }],
    iconPk: true,
    iconPhone: true
  },
  {
    avatar: 'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '猜时间的彩',
    desc: '都快要在美颜崩盘~',
    tags: [{ text: '成都' }, { text: '高颜值' }],
    iconPk: true,
    iconPhone: true
  },
  {
    avatar: 'https://images.unsplash.com/photo-1519340333755-c1aa5571fd46?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '记录生活美与分享~',
    tags: [{ text: '重庆' }, { text: '才艺' }],
    iconPk: true,
    iconPhone: true
  },
  {
    avatar: 'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '甜美声线，互动多多',
    tags: [{ text: '杭州' }, { text: '甜妹' }],
    iconPk: true,
    iconPhone: true
  },
  {
    avatar: 'https://images.unsplash.com/photo-1529626455594-4ff0802cfb7e?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '元气少女，活力满满',
    tags: [{ text: '苏州' }, { text: '元气' }],
    iconPk: true,
    iconPhone: true
  },
  {
    avatar: 'https://images.unsplash.com/photo-1519985176271-adb1088fa94c?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '高冷御姐，气场全开',
    tags: [{ text: '天津' }, { text: '御姐' }],
    iconPk: true,
    iconPhone: true
  },
  {
    avatar: 'https://images.unsplash.com/photo-1463453091185-61582044d556?auto=format&fit=facearea&w=256&h=256&q=80',
    title: '一分快三',
    desc: '文艺青年，诗和远方',
    tags: [{ text: '厦门' }, { text: '文艺' }],
    iconPk: true,
    iconPhone: true
  }
]);

// Watch for route query changes
watch(
  () => route.query,
  (newQuery) => {
    showProfileSheet.value = newQuery.view === 'profile';
    showLeaderboardSheet.value = newQuery.view === 'leaderboard';
    showWatchListSheet.value = newQuery.view === 'watchlist';
    showVipMembersSheet.value = newQuery.view === 'vipmembers';
  },
  { immediate: true }
);

// Watch action sheets to update URL
watch(
  [showProfileSheet, showLeaderboardSheet, showWatchListSheet, showVipMembersSheet],
  ([profile, leaderboard, watchlist, vipmembers]) => {
    const currentView = route.query.view;
    let newView = null;

    if (profile) newView = 'profile';
    if (leaderboard) newView = 'leaderboard';
    if (watchlist) newView = 'watchlist';
    if (vipmembers) newView = 'vipmembers';

    if (newView !== currentView) {
      const { view, ...otherQuery } = route.query;
      const query = newView ? { ...otherQuery, view: newView } : otherQuery;
      router.push({ path: route.path, query });
    }
  }
);

// Event handlers
const handleHostAction = () => {
  showToast('主播互动');
};

const handleIconAction = (action) => {
  openningBottomItem.value = 'bottomPopup';
};

const handleToolChange = (tool) => {
  bottomPopupActiveTool.value = tool;
};

const handleVipCharge = () => {
  showToast('VIP充值');
};

const handleSettings = () => {
  openSettings();
};

const handleCommentInput = () => {
  openningBottomItem.value = 'commentInput';
};

const handleSwitchToGame = () => {
  showToast('切换到游戏');
};

// Update live timer
const updateLiveTimer = () => {
  const [minutes, seconds] = liveTimer.value.split(':').map(Number);
  const totalSeconds = minutes * 60 + seconds + 1;
  const newMinutes = Math.floor(totalSeconds / 60);
  const newSeconds = totalSeconds % 60;
  liveTimer.value = `${newMinutes.toString().padStart(2, '0')}:${newSeconds.toString().padStart(2, '0')}`;
};

let timerInterval;

onMounted(() => {
  timerInterval = setInterval(updateLiveTimer, 1000);
});

onUnmounted(() => {
  if (timerInterval) clearInterval(timerInterval);
});
</script>

<style scoped>
/* No component-specific styles needed since they're in individual components */
</style>
