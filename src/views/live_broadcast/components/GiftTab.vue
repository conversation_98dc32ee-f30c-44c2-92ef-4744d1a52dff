<template>
  <div class="game-content">
    <div v-for="i in 8" :key="i" :class="{ 'gift-item': true, selected: selectedGift === i }" @click="selectedGift = i">
      <div class="gift-image">
        <img :src="`src/assets/live/gift_${i}.png`" alt="Ads 1" class="game-image" />
      </div>
      <div class="gift-info">
        <div class="gift-name">火箭筒</div>
        <div class="gift-price">
          <img src="@/assets/live/diamond_icon.png" alt="Diamond" class="diamond-icon" />
          <span class="price">9999</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const selectedGift = ref(null);
</script>

<style scoped>
.game-content {
  padding: 8px 0px;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 0px;
}

.selected.gift-item {
  border-color: #00eaff;
}

.gift-item {
  border: 2px solid transparent;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-around;
  gap: 4px;
  padding: 10px 0px;
  border-radius: 5px;
}

.gift-image {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.game-image {
  width: 45px;
  height: auto;
}

.gift-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.gift-name {
  font-size: 12px;
  color: white;
  font-weight: bold;
}

.gift-price {
  display: flex;
  align-items: center;
  gap: 4px;
}

.diamond-icon {
  width: 15px;
  height: 15px;
}

.price {
  font-size: 12px;
  color: white;
  font-weight: bold;
}

.selected .price {
  color: #00eaff;
}
</style>
