<template>
  <div class="px-4 flex items-center justify-between">
    <div class="flex items-center gap-2">
      <div class="relative" @click="handleClick">
        <img :src="user.avatar" class="w-[55px] rounded-full border-[3px] border-solid border-[#993ee8]" />
        <img src="@/assets/live/guardian_icon_3.png" class="absolute bottom-0 left-1/2 -translate-x-1/2 w-[35px]" />
      </div>

      <div class="flex flex-col gap-1">
        <img src="@/assets/live/guardian_label.png" class="h-[25px] w-fit" />
        <div class="text-[#333333] text-[14px] font-bold">小纯御专属走A怪</div>
      </div>
    </div>
    <div class="flex flex-col gap-2">
      <div class="px-4 py-1 bg-[#72a4eb1a] rounded-3xl text-[12px] font-bold flex items-center gap-4">
        <div class="text-[#333333]">上周贡献</div>
        <span class="color-[#ff9c00]">520500</span>
      </div>
      <div class="px-4 py-1 bg-[#72a4eb1a] rounded-3xl text-[12px] font-bold flex items-center gap-4">
        <div class="text-[#333333]">守护天数</div>
        <span class="color-[#18c8ad]">365</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router';
const props = defineProps({
  user: {
    type: Object,
    default: () => ({
      name: '小纯御',
      avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
      level: 'LV15'
    })
  }
});

const router = useRouter();

const handleClick = () => {
  router.push(`/live_broadcast/guardian_info`);
};
</script>

<style scoped></style>
