<template>
  <van-action-sheet v-model:show="visible" color="#000" :closeable="true" close-icon="cross" :style="{
    '--van-popup-background': '#fff',
    '--van-action-sheet-item-text-color': '#000',
    'height': '400px'
  }">
    <div class="flex flex-col gap-y-4 px-2.5 pt-6">
      <!-- item -->
      <div class="flex justify-between items-center pl-2.5 watchlist-item">
        <div class="flex items-center gap-x-6">
          <div class="relative">
            <van-image round width="59" height="59" fit="cover" src="/static/webp/avatar8.webp"
              class="ring-2 ring-[#28b5fd] block" />
            <div class="absolute -top-[20%] -left-[20%] w-[140%] h-[140%]">
              <img class="block" src="@/assets/live/ava_medal_01.png" />
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex items-center gap-1.5">
              <div class="font-bold">抚子姐姐</div>
              <div class="flex items-center -space-x-3">
                <van-image class="block w-7" :src="getRankIcon(2)" fit="cover" />
                <div
                  class="text-white text-[8px] font-bold bg-[url('@/assets/images/label_medal_01.png')] bg-no-repeat bg-center bg-cover min-w-9 py-[1px] pl-3.5 pr-2">
                  LV53
                </div>
              </div>
            </div>
            <div class="flex items-center gap-3">
              <img class="block" width="21" height="21" src="@/assets/live/gender_01.png" fit="cover" />
              <div class="text-xs font-medium">会员ID: 221564868</div>
            </div>
          </div>
        </div>
        <button
          class="text-[#28b5fd] bg-[#d8f2ff] border-none rounded-full text-xs w-[62px] h-[30px] p-0">已关注</button>
      </div>
      <!-- /item -->
      <div v-for="i in 10" :key="i" class="flex justify-between items-center pl-2.5 watchlist-item">
        <div class="flex items-center gap-x-6">
          <div class="relative">
            <van-image round width="59" height="59" fit="cover" src="/static/webp/avatar8.webp"
              class="ring-2 ring-[#28b5fd] block" />
          </div>
          <div class="space-y-2">
            <div class="flex items-center gap-1.5">
              <div class="font-bold">抚子姐姐</div>
              <div class="flex items-center -space-x-3">
                <van-image class="block w-7" :src="getRankIcon(2)" fit="cover" />
                <div
                  class="text-white text-[8px] font-bold bg-[url('@/assets/images/label_medal_01.png')] bg-no-repeat bg-center bg-cover min-w-9 py-[1px] pl-3.5 pr-2">
                  LV53
                </div>
              </div>
            </div>
            <div class="flex items-center gap-3">
              <img class="block" width="21" height="21" src="@/assets/live/gender_01.png" fit="cover" />
              <div class="text-xs font-medium">会员ID: 221564868</div>
            </div>
          </div>
        </div>
        <button
          class="text-[#28b5fd] bg-[#d8f2ff] border-none rounded-full text-xs w-[62px] h-[30px] p-0">已关注</button>
      </div>
    </div>
  </van-action-sheet>
</template>

<script setup>
// import icMedal01 from '@/assets/images/icon_medal_01.png';
// import icMedal02 from '@/assets/images/icon_medal_02.png';
// import icMedal03 from '@/assets/images/icon_medal_03.png';
// import icMedal04 from '@/assets/images/icon_medal_04.png';
// import icMedal05 from '@/assets/images/icon_medal_05.png';
// import gender01 from '@/assets/images/gender_01.png';
// import avaMedal01 from '@/assets/images/ava_medal_01.png';
import { getRankIcon } from '@/utils/vipLv';
defineOptions({
  name: 'WatchListActionSheet'
})

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'action'])

const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const handleAction = () => {
  emit('action', 'watchlist-test', { message: 'Watch list action triggered' })
}
</script>
