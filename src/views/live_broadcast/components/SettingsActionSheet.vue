<template>
  <van-action-sheet
    v-model:show="localShow"
    :style="{ 
      height: '60vh', 
      '--van-popup-background': 'rgba(0, 0, 0, 0.5)'
    }"
    round
    teleport="body"
    :close-on-click-overlay="true"
  >
    <div class="relative text-white h-full font-[var(--van-base-font)]">

      <!-- Settings grid -->
      <div class="p-4">
        <div class="grid grid-cols-5 gap-2 gap-y-4 mb-4">
          <div 
            v-for="setting in settings" 
            :key="setting.key"
            class="flex flex-col items-center space-y-2"
            @click="toggleSetting(setting.key)"
          >
            <div class="w-8 h-8 flex items-center justify-center">
              <img 
                :src="setting.isActive ? setting.activeIcon : setting.inactiveIcon" 
                class="w-full h-full object-contain" 
                :alt="setting.label"
              />
            </div>
            <span class="text-xs text-center">{{ setting.label }}</span>
          </div>
        </div>

        <!-- Sliders section -->
        <div class="space-y-6">
          <!-- Background opacity slider -->
          <div class="bg-[rgba(0,0,0,0.2)] border-0.5 border-[rgba(255,255,255,0.2)] border-solid rounded-2xl p-4">
            <div class="flex items-center mb-3">
              <span class="text-sm">聊天消息背景透明度</span>
              <span class="text-sm ml-2">{{ backgroundOpacityModel }}%</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="flex-1 relative">
                <van-slider
                  v-model="backgroundOpacityModel"
                  :min="0"
                  :max="100"
                  :step="10"
                  bar-height="22px"
                  button-size="20px"
                  active-color="#2EBDFE"
                  inactive-color="#fff"
                  class="custom-slider"
                />

                <!-- <div class="flex justify-between -mt-4.5 text-xs text-black opacity-20">
                  <span v-for="num in 6" :key="num" :class="num === 1 || num === 6 ? 'opacity-0' : ''">|</span>
                </div> -->
              </div>
            </div>
          </div>

          <!-- Font size slider -->
          <div class="bg-[rgba(0,0,0,0.2)] border-0.5 border-[rgba(255,255,255,0.2)] border-solid rounded-2xl p-4">
            <div class="flex items-center justify-between mb-3">
              <span class="text-sm">聊天消息字体大小</span>
            </div>
            <div class="flex items-center space-x-3">
              <div class="flex-1 relative">
                <van-slider
                  v-model="fontSizeModel"
                  :min="18"
                  :max="24"
                  :step="1"
                  bar-height="22px"
                  button-size="20px"
                  active-color="#2EBDFE"
                  inactive-color="#fff"
                  class="custom-slider"
                />
                <!-- <div class="flex justify-between -mt-4.5 text-xs text-black opacity-20">
                  <span v-for="size in 7" :key="size">|</span>
                </div>
                <div class="flex justify-between -mt-4.5 text-xs text-black opacity-80">
                  <span>A-</span>
                  <span>A+</span>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </van-action-sheet>
</template>

<script setup>
import { computed } from 'vue'
import { useSettings } from '@/composables/useSettings'
import { getAllSettingsWithUIConfig } from '@/constants/settings'
import { iconSettingClose } from '@/assets/live/icons.js'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show'])

const localShow = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

// Use settings composable
const {
  settingsStore,
  backgroundOpacity,
  fontSize,
  toggleSetting,
  setBackgroundOpacity,
  setFontSize
} = useSettings()

// Computed properties for reactive data with setters
const backgroundOpacityModel = computed({
  get: () => backgroundOpacity.value,
  set: (value) => setBackgroundOpacity(value)
})

const fontSizeModel = computed({
  get: () => fontSize.value,
  set: (value) => setFontSize(value)
})

// Combine store state with UI config
const settings = computed(() => getAllSettingsWithUIConfig(settingsStore.settings))
</script>

<style scoped>
:deep(.custom-slider .van-slider__bar) {
  border-radius: 22px;
}

</style> 