<template>
  <div class="live-comments">
    <div v-for="comment in liveComments" :key="comment.id" class="comment-item">
      <UserCommentContent v-if="comment.type === 'user'" :comment="comment.data" />
      <BettingCommentContent v-else-if="comment.type === 'betting'" :comment="comment.data" @switch-to-game="handleSwitchToGame" />
      <WinningCommentContent v-else-if="comment.type === 'winning'" :comment="comment.data" />
      <NoticeComments v-else-if="comment.type === 'notice'" :notices="comment.data" />
    </div>
  </div>
</template>

<script setup>
import UserCommentContent from '@/views/video_details/children/comment_types/UserCommentContent.vue';
import BettingCommentContent from '@/views/video_details/children/comment_types/BettingCommentContent.vue';
import WinningCommentContent from '@/views/video_details/children/comment_types/WinningCommentContent.vue';
import NoticeComments from '@/views/video_details/children/comment_types/NoticeComments.vue';

const props = defineProps({});

// Live comments data
const liveComments = ref([
  {
    id: '4',
    type: 'notice',
    data: ['<span class="text-[#ffd631]">发财大世豪</span> 在一分钟一口气进了'],
    timestamp: Date.now()
  },
  {
    id: '1',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '小纯御御姐 送出【神话一下】×1',
        user_info: {
          nickname: '系统',
          level: 10,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  {
    id: '2',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '小可爱 关注了小纯御',
        user_info: {
          nickname: '系统',
          level: 1,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  {
    id: '3',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '发财大世豪 在一分钟一口气进了',
        user_info: {
          nickname: '系统',
          level: 20,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  // winning comment
  {
    id: '5',
    type: 'winning',
    data: {
      client_id: 'Game',
      data: {
        anchor_id: '2727',
        code: 0,
        data: {
          game_name: '一分快三',
          money: '10.00',
          target: '2727',
          user_id: 2809115,
          user_name: '发财大世豪'
        },
        game_id: 1,
        game_type: 'WIN_MESSAGE',
        liveId: '297703',
        msg: 'success',
        target: '2727'
      },
      event_type: 6,
      uuid: '1934600597685751808'
    },
    timestamp: Date.now()
  },
  {
    id: '1',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '小纯御御姐 送出【神话一下】×1',
        user_info: {
          nickname: '系统',
          level: 10,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  {
    id: '2',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '小可爱 关注了小纯御',
        user_info: {
          nickname: '系统',
          level: 1,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  {
    id: '3',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '发财大世豪 在一分钟一口气进了',
        user_info: {
          nickname: '系统',
          level: 20,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  // winning comment
  {
    id: '5',
    type: 'winning',
    data: {
      client_id: 'Game',
      data: {
        anchor_id: '2727',
        code: 0,
        data: {
          game_name: '一分快三',
          money: '10.00',
          target: '2727',
          user_id: 2809115,
          user_name: '发财大世豪'
        },
        game_id: 1,
        game_type: 'WIN_MESSAGE',
        liveId: '297703',
        msg: 'success',
        target: '2727'
      },
      event_type: 6,
      uuid: '1934600597685751808'
    },
    timestamp: Date.now()
  },
  {
    id: '1',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '小纯御御姐 送出【神话一下】×1',
        user_info: {
          nickname: '系统',
          level: 10,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  {
    id: '2',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '小可爱 关注了小纯御',
        user_info: {
          nickname: '系统',
          level: 1,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  {
    id: '3',
    type: 'user',
    data: {
      data: {
        type: 1,
        content: '发财大世豪 在一分钟一口气进了',
        user_info: {
          nickname: '系统',
          level: 20,
          levelIcon: '',
          avatar: '/api/placeholder/30/30'
        }
      }
    },
    timestamp: Date.now()
  },
  // winning comment
  {
    id: '5',
    type: 'winning',
    data: {
      client_id: 'Game',
      data: {
        anchor_id: '2727',
        code: 0,
        data: {
          game_name: '一分快三',
          money: '10.00',
          target: '2727',
          user_id: 2809115,
          user_name: '发财大世豪'
        },
        game_id: 1,
        game_type: 'WIN_MESSAGE',
        liveId: '297703',
        msg: 'success',
        target: '2727'
      },
      event_type: 6,
      uuid: '1934600597685751808'
    },
    timestamp: Date.now()
  }
]);

const emit = defineEmits(['switch-to-game']);

const handleSwitchToGame = () => {
  emit('switch-to-game');
};
</script>

<style scoped>
.live-comments {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-height: 300px;
  overflow-y: auto;
  pointer-events: auto;
}

.comment-item {
  background: rgba(0, 0, 0, 0.3);
  padding: 2px 4px;
  border-radius: 16px;
  font-size: 12px;
  max-width: 80%;
  width: fit-content;
}
</style>
