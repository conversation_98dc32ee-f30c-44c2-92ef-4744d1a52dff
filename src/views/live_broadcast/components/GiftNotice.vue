<template>
  <div class="new-user-join-container">
    <div class="background">
      <img src="@/assets/live/bg_new_user_join_example.png" alt="New User" />
    </div>
    <div class="new-user-join-content">
      <!-- User Avatar with Badge -->
      <div class="avatar-container">
        <img :src="userData.data.data.user_info.avatar" alt="User Avatar" class="user-avatar" />
      </div>

      <!-- User Info -->
      <div class="user-info">
        <div class="user-details">
          <span class="username">{{ userData.data.data.user_info.nickname }}</span>
        </div>
        <div class="join-message">
          赠送
          <span class="text-[#ffd631]">火箭筒</span>
        </div>
      </div>

      <div class="new-user-icon-container">
        <img src="@/assets/live/gift_2.png" alt="New User" class="new-user-icon" />
      </div>

      <div class="amount">
        <span class="amount-multiply">x</span>
        <span class="amount-number">99</span>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  userData: {
    type: Object,
    default: () => ({
      id: '3',
      type: 'user',
      data: {
        data: {
          type: 1,
          content: '李大富 在一分钟一口气进了',
          user_info: {
            nickname: '李大富',
            level: 8,
            levelIcon: '',
            avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face'
          }
        }
      },
      timestamp: Date.now()
    })
  }
});

const emit = defineEmits([]);
</script>

<style scoped>
.new-user-join-container {
  display: flex;
  align-items: center;
  margin: 8px 0;
  position: relative;
}

.new-user-join-content {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  padding: 4px;
  border-radius: 20px;
  gap: 10px;
}

.avatar-container {
  position: relative;
  flex-shrink: 0;
}

.user-avatar {
  max-height: 30px;

  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.user-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.user-details {
  display: flex;
  align-items: center;
  gap: 2px;
}

.username {
  font-size: 12px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

.level-badge {
  display: flex;
  align-items: center;
  position: relative;
}

.level-icon {
  width: 32px;
  height: 18px;
  object-fit: contain;
}

.join-message {
  font-size: 10px;
  color: #ffffff;
  opacity: 0.95;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  font-weight: 500;
}

.new-user-icon-container {
  position: relative;
  flex-shrink: 0;
  padding-left: 20px;
}

.new-user-icon {
  width: auto;
  height: 45px;
  object-fit: contain;
}
.amount {
  display: flex;
  align-items: center;
  gap: 2px;
  color: #ffffff;
}
.amount-multiply {
  font-size: 15px;
}
.amount-number {
  font-size: 24px;
  color: #ffffff;
  opacity: 0.95;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  font-weight: bold;
}
</style>
