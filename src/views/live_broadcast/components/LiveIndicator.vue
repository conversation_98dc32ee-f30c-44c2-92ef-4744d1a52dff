<template>
  <div class="live-indicator">
    <div class="live-badge">
      <van-icon name="play-circle" size="14" color="white" />
      <span>LIVE</span>
    </div>
    <div class="streamer-info">
      <span class="streamer-title">{{ streamerTitle }}</span>
      <span class="room-entry">进入直播间</span>
    </div>
  </div>
</template>

<script setup>
// Props
defineProps({
  streamerTitle: {
    type: String,
    default: '季大哥'
  }
})
</script>

<style scoped>
/* Live Indicator */
.live-indicator {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 255, 0.8);
  padding: 8px 12px;
  border-radius: 20px;
}

.live-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: bold;
}

.streamer-info {
  display: flex;
  flex-direction: column;
  font-size: 12px;
}

.streamer-title {
  font-weight: bold;
}

.room-entry {
  color: rgba(255, 255, 255, 0.8);
  font-size: 10px;
}
</style> 