<template>
  <div class="settings-demo p-4 bg-gray-900 text-white rounded-lg">
    <h3 class="text-lg font-bold mb-4">Settings Demo (useSettings composable)</h3>
    
    <!-- Current values -->
    <div class="mb-4">
      <h4 class="text-md mb-2">Current Values:</h4>
      <div class="text-sm space-y-1">
        <p>Background Opacity: {{ backgroundOpacity }}%</p>
        <p>Font Size: {{ fontSize }}px</p>
        <p>Active Settings: {{ activeSettingsKeys.join(', ') }}</p>
      </div>
    </div>
    
    <!-- Quick toggles -->
    <div class="mb-4">
      <h4 class="text-md mb-2">Quick Toggles:</h4>
      <div class="flex flex-wrap gap-2">
        <button 
          @click="toggleSetting('gift')"
          :class="['px-3 py-1 text-xs rounded', isGiftEffectActive ? 'bg-green-600' : 'bg-gray-600']"
        >
          Gift Effect: {{ isGiftEffectActive ? 'ON' : 'OFF' }}
        </button>
        <button 
          @click="toggleSetting('sound')"
          :class="['px-3 py-1 text-xs rounded', isSoundActive ? 'bg-green-600' : 'bg-gray-600']"
        >
          Sound: {{ isSoundActive ? 'ON' : 'OFF' }}
        </button>
        <button 
          @click="toggleSetting('stealth')"
          :class="['px-3 py-1 text-xs rounded', isStealthActive ? 'bg-green-600' : 'bg-gray-600']"
        >
          Stealth: {{ isStealthActive ? 'ON' : 'OFF' }}
        </button>
      </div>
    </div>
    
    <!-- Utility functions demo -->
    <div class="mb-4">
      <h4 class="text-md mb-2">Utility Functions:</h4>
      <div class="text-sm space-y-1">
        <p>Should show gift effect: {{ shouldShowGiftEffect() }}</p>
        <p>Should play sound: {{ shouldPlaySound() }}</p>
        <p>Should use stealth mode: {{ shouldUseStealthMode() }}</p>
      </div>
    </div>
    
    <!-- Sample chat message with applied styles -->
    <div class="mb-4">
      <h4 class="text-md mb-2">Sample Chat Message:</h4>
      <div 
        class="p-2 rounded border border-gray-500"
        :style="chatStyle"
      >
        <span class="font-bold text-yellow-400">Demo User:</span>
        <span>This message uses dynamic settings styles!</span>
      </div>
    </div>
    
    <!-- Actions -->
    <div class="flex gap-2">
      <button 
        @click="resetSettings()"
        class="px-3 py-1 bg-red-600 text-white text-sm rounded"
      >
        Reset All Settings
      </button>
      <button 
        @click="randomizeSettings()"
        class="px-3 py-1 bg-blue-600 text-white text-sm rounded"
      >
        Randomize Settings
      </button>
    </div>
  </div>
</template>

<script setup>
import { useSettings } from '@/composables/useSettings'

// Use settings composable
const {
  backgroundOpacity,
  fontSize,
  chatStyle,
  activeSettingsKeys,
  isGiftEffectActive,
  isSoundActive,
  isStealthActive,
  toggleSetting,
  resetSettings,
  setBackgroundOpacity,
  setFontSize,
  setSetting,
  shouldShowGiftEffect,
  shouldPlaySound,
  shouldUseStealthMode
} = useSettings()

// Demo function to randomize settings
const randomizeSettings = () => {
  setBackgroundOpacity(Math.floor(Math.random() * 101))
  setFontSize(Math.floor(Math.random() * 13) + 18)
  
  // Randomize some settings
  setSetting('gift', Math.random() > 0.5)
  setSetting('sound', Math.random() > 0.5)
  setSetting('stealth', Math.random() > 0.5)
  setSetting('vibration', Math.random() > 0.5)
}
</script>

<style scoped>
.settings-demo {
  max-width: 600px;
  margin: 0 auto;
}
</style> 