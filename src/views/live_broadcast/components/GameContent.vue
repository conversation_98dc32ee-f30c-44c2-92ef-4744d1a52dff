<template>
  <div :style="{ height: `${props.height}px` }" class="game-content-container">
    <van-tabs
      v-model="activeTab"
      color="#00eaff"
      line-width="20"
      title-active-color="#00eaff"
      title-inactive-color="white"
      background="transparent"
      shrink
    >
      <van-tab title="热门">
        <div class="tab-content" :style="{ height: `${props.height - 110}px` }"><GameTab /></div>
      </van-tab>
      <van-tab title="电子"
        ><div class="tab-content" :style="{ height: `${props.height - 110}px` }"><GameTab /></div
      ></van-tab>
      <van-tab title="棋牌"
        ><div class="tab-content" :style="{ height: `${props.height - 110}px` }"><GameTab /></div
      ></van-tab>
      <van-tab title="体育"
        ><div class="tab-content" :style="{ height: `${props.height - 110}px` }"><GameTab /></div
      ></van-tab>
      <van-tab title="视讯"
        ><div class="tab-content" :style="{ height: `${props.height - 110}px` }"><GameTab /></div
      ></van-tab>
      <van-tab title="捕鱼"
        ><div class="tab-content" :style="{ height: `${props.height - 110}px` }"><GameTab /></div
      ></van-tab>
    </van-tabs>
    <div class="game-info">
      <div class="balance-info">
        <img class="balance-icon" src="@/assets/live/balance_icon.png" />
        <div class="balance-text">280000</div>
        <img class="reload-icon" src="@/assets/live/reload_icon.png" />
      </div>
      <div class="game-actions">
        <van-button class="recharge-btn button" size="small" type="primary">充值</van-button>
        <van-button class="game-center-btn button" size="small" type="primary">游戏中心</van-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import GameTab from './GameTab.vue';

const props = defineProps({
  height: {
    type: Number,
    default: 270
  }
});

const activeTab = ref(0);
</script>

<style scoped>
.game-content-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.tab-content {
  overflow-y: scroll;
  padding: 8px 12px;
}

.game-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
}

.balance-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.balance-icon {
  width: 15px;
  height: 15px;
}

.balance-text {
  color: white;
  font-size: 13px;
  font-weight: bold;
  color: #ff9c00;
}

.reload-icon {
  width: 11px;
  height: 11px;
}

.game-actions {
  display: flex;
  gap: 8px;
}

.button {
  border-radius: 20px;
  padding: 2px 10px;
  font-size: 12px;
  height: 30px;
  min-width: 80px;
  color: white;
  border: none;
}

.recharge-btn {
  background: #11cfff;
}

.game-center-btn {
  background: #966cf7;
}
</style>
