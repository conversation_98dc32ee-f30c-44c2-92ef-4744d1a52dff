<template>
  <van-popup
    v-model:show="localShow"
    position="center"
    :style="{ width: '90%', maxWidth: '400px', background: 'transparent'}"
    round
    teleport="body"
    :close-on-click-overlay="true"
  >
    <div class="relative font-[var(--van-base-font)]">
      <!-- Background gradient -->
      <div class="absolute inset-0 bg-[url('@/assets/live/bg-popup.png')] bg-no-repeat bg-contain bg-bottom"></div>
      <!-- Content -->
      <div class="relative z-10 text-white pb-8 px-4">
        <!-- Header with avatar and name -->
        <div class="flex flex-col items-center mb-4 mt-4">
          <!-- Avatar with dotted border -->
          <div class="relative mb-2">
            <div class="rounded-full border-4 border-solid border-white flex items-center justify-center">
              <img 
                :src="cardData.avatar" 
                :alt="cardData.name"
                class="w-20 h-20 rounded-full object-cover"
              />
            </div>
          </div>
          
          <!-- Name -->
          <h3 class="text-xl font-bold">{{ cardData.name }}</h3>
          <p class="text-sm">{{ cardData.subtitle }}</p>
        </div>

        <!-- Action buttons -->
        <div class="flex items-center justify-between space-x-4 mb-6 p-0.5 bg-white rounded-full border-4 border-solid border-[#F84A75]">
          <!-- Phone button -->
          <button class="bg-[#FFF1EE] border-none rounded-full flex items-center justify-center">
            <img src="@/assets/live/icon-phone-popup.png" class="w-10 h-10" />
          </button>

          <div class="flex items-center justify-center text-[#FF5000]">090394039</div>
          
          <!-- Get button -->
          <button class="border-none bg-gradient-to-r from-[#FFECAB] to-[#FFCD52] text-white px-8 py-2 rounded-full font-medium">
            获取
          </button>
        </div>

        <!-- Progress section -->
        <div class="p-4 pt-6 mb-6 text-[#873323]">
          <div class="text-center mb-3">
            <h4 class="text-lg font-bold mb-1">获取名片进度</h4>
            <p class="text-sm">赠送礼物达到要求即可获取</p>
          </div>
          
          <!-- Progress bar -->
          <div class="relative">
            <div class="w-full rounded-full h-2 mb-2 px-8">
              <div 
                class="bg-gradient-to-r from-[#FF0047] to-[#FFAE9B] h-2 rounded-full transition-all duration-300"
                :style="{ width: `${progressPercentage}%` }"
              ></div>
            </div>
            <div class="flex justify-center text-sm">
              <span>{{ cardData.currentProgress }}</span>/
              <span>{{ cardData.totalProgress }}</span>
            </div>
          </div>
        </div>

        <!-- Instructions -->
        <div class="text-sm text-white/90 space-y-1">
          <p>1、添加时请备注昵称避免主播无法区分</p>
          <p>2、联系方式如有虚假可通过客服投诉</p>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:show']);

const localShow = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

// Card data
const cardData = ref({
  name: '小六月',
  subtitle: '迷',
  rank: '8',
  avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
  currentProgress: '1006',
  totalProgress: '1314'
});

// Calculate progress percentage
const progressPercentage = computed(() => {
  const current = parseInt(cardData.value.currentProgress);
  const total = parseInt(cardData.value.totalProgress);
  return Math.min((current / total) * 100, 100);
});
</script>

<style scoped>
/* Custom gradient background */
.bg-gradient-to-br {
  background: linear-gradient(135deg, #f472b6 0%, #ec4899 50%, #fb923c 100%);
}

/* Backdrop blur effect */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Button hover effects */
button:active {
  transform: scale(0.95);
}
</style> 