<template>
  <div class="floating-host-info">
    <div class="host-info">
      <div class="host-avatar">
        <img :src="hostInfo.avatar" :alt="hostInfo.name" />
      </div>
      <div class="host-details">
        <span class="host-name">{{ hostInfo.name }}</span>
        <span class="host-status">{{ hostInfo.status }}</span>
      </div>
      <van-button 
        size="small" 
        type="primary"
        class="host-action-btn"
        @click="handleHostAction"
      >
        <van-icon name="plus" size="14" />
      </van-button>
    </div>

    <!-- Additional Actions -->
    <div class="additional-actions">
      <span class="action-text">主播关系更近一步</span>
      <van-button size="small" type="primary" class="unlock-btn">
        去解锁
      </van-button>
    </div>
  </div>
</template>

<script setup>
// Props
defineProps({
  hostInfo: {
    type: Object,
    default: () => ({
      name: '小纯御',
      avatar: '/api/placeholder/40/40',
      status: '关注主播不迷路'
    })
  }
})

// Emits
const emit = defineEmits(['hostAction'])

// Event handlers
const handleHostAction = () => {
  emit('hostAction')
}
</script>

<style scoped>
/* Floating Host Information */
.floating-host-info {
  position: absolute;
  bottom: 250px;
  left: 16px;
  display: flex;
  flex-direction: column;
  gap: 12px;
  pointer-events: auto;
}

.host-info {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 8px 12px;
  border-radius: 20px;
  width: fit-content;
}

.host-avatar {
  width: 32px;
  height: 32px;
}

.host-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.host-details {
  display: flex;
  flex-direction: column;
  color: black;
}

.host-name {
  font-weight: bold;
  font-size: 12px;
}

.host-status {
  font-size: 10px;
  color: #666;
}

.host-action-btn {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background: #007aff;
  border: none;
  color: white;
}

.additional-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(138, 43, 226, 0.8);
  padding: 8px 12px;
  border-radius: 20px;
  width: fit-content;
}

.action-text {
  color: white;
  font-size: 12px;
}

.unlock-btn {
  background: white;
  color: #8a2be2;
  border-radius: 12px;
  font-size: 12px;
  min-width: 50px;
  height: 24px;
}
</style> 