<template>
  <van-action-sheet v-model:show="localShow" :close-on-click-overlay="true" round teleport="body">
    <GuardianPackage v-if="showingType === 'package'" />
    <GuardianList v-if="showingType === 'list'" @upgrade="showingType = 'package'" />
  </van-action-sheet>
</template>

<script setup>
import { ref, computed } from 'vue';
import { getRankIcon } from '@/utils/vipLv';
import GuardianPackage from './GuardianPackage.vue';
import GuardianList from './GuardianList.vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const showingType = ref('list'); // 'package', 'list'

const emit = defineEmits(['update:show']);

const localShow = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});
</script>

<style scoped></style>
