<template>
  <div class="w-full max-w-md mx-auto">
    <div>
      <div class="flex justify-center items-start gap-x-7">
        <div class="flex flex-col items-center mt-[75px]">
          <div class="relative w-[44px] h-[44px]">
            <van-image round width="44" height="44" fit="cover" src="https://randomuser.me/api/portraits/women/44.jpg"
              class="block w-[44px] h-[44px]" />
            <img class="absolute bottom-0 left-0 w-[45px] h-[57px]" src="@/assets/live/skin_icon_ranking_second.png" />
          </div>

          <div class="text-white flex items-center gap-x-2 mt-0.5">
            <img width="11" height="13" fit="cover" src="@/assets/live/icon_fire.png" />
            <div class="text-xs font-bold">逸臣</div>
            <div class="text-sm font-bold">5876</div>
          </div>
          <div class="text-sm text-white font-bold">阳光小陈</div>
          <div class="bg-black/30 text-white text-xs rounded-full px-2 mt-1">
            胜2/总8
          </div>
        </div>
        <div class="flex flex-col items-center mt-[43px]">
          <div class="relative w-[62px] h-[62px]">
            <van-image round fit="cover" src="https://randomuser.me/api/portraits/men/32.jpg"
              class="block w-[62px] h-[62px]" />
            <img class="absolute bottom-0 left-0 w-[63px] h-[81px]" fit="cover"
              src="@/assets/live/skin_icon_ranking_first.png" />
          </div>
          <div class="text-sm text-white font-bold mt-[21px]">不帮抓就摆烂</div>
          <div class="bg-black/30 text-white text-xs rounded-full px-2 mt-1">
            胜2/总8
          </div>
        </div>
        <div class="flex flex-col items-center mt-[95px]">
          <div class="relative w-[44px] h-[44px]">
            <van-image round fit="cover" src="https://randomuser.me/api/portraits/women/65.jpg"
              class="block w-[44px] h-[44px]" />
            <img class="absolute bottom-0 left-0 w-[45px] h-[57px]" src="@/assets/live/skin_icon_ranking_third.png" />
          </div>
          <div class="text-white flex items-center gap-x-2 mt-0.5">
            <img width="11" height="13" fit="cover" src="@/assets/live/icon_fire.png" />
            <div class="text-xs font-bold">逸臣</div>
            <div class="text-sm font-bold">6890</div>
          </div>
          <div class="text-sm text-white font-bold mt-0.5">全力以赴</div>
          <div class="bg-black/30 text-white text-xs rounded-full px-2 mt-1">
            胜2/总8
          </div>
        </div>
      </div>
      <div class="bg-white rounded-t-2xl mt-[37px] pt-2.5 pb-2 flex flex-col gap-y-[3px]">
        <div class="flex justify-between items-center pl-4 pr-5 py-[17px] border-b last:border-b-0">
          <div class="flex items-center gap-x-2">
            <div class="w-8 text-right font-bold mr-2">04.</div>
            <van-image round width="35" height="35" fit="cover" src="https://randomuser.me/api/portraits/men/12.jpg"
              class="mr-3" />
            <div class="flex items-center gap-x-2">
              <div class="font-semibold text-[15px]">朝气代表</div>
              <div class="inline-block bg-[#66ccff] text-white text-[9px] rounded-full px-1 py-0">
                控分/8场
              </div>
            </div>
          </div>

          <div class="flex items-center gap-x-2">
            <img width="11" height="13" fit="cover" src="@/assets/live/icon_fire.png" />
            <div class="text-xs font-bold">逸臣</div>
            <div class="text-xs font-bold">980</div>
          </div>
        </div>
        <div class="flex justify-between items-center pl-4 pr-5 py-[17px] border-b last:border-b-0">
          <div class="flex items-center gap-x-2">
            <div class="w-8 text-right font-bold mr-2">05.</div>
            <van-image round width="35" height="35" fit="cover" src="https://randomuser.me/api/portraits/women/13.jpg"
              class="mr-3" />
            <div class="flex items-center gap-x-2">
              <div class="font-semibold text-[15px]">踏浪而行</div>
              <div class="inline-block bg-[#66ccff] text-white text-[9px] rounded-full px-1 py-0">
                控分/8场
              </div>
            </div>
          </div>
          <div class="flex items-center gap-x-2">
            <img width="11" height="13" fit="cover" src="@/assets/live/icon_fire.png" />
            <div class="text-xs font-bold">逸臣</div>
            <div class="text-xs font-bold">980</div>
          </div>
        </div>
        <div class="flex justify-between items-center pl-4 pr-5 py-[17px] border-b last:border-b-0">
          <div class="flex items-center gap-x-2">
            <div class="w-8 text-right font-bold mr-2">06.</div>
            <van-image round width="35" height="35" fit="cover" src="https://randomuser.me/api/portraits/women/14.jpg"
              class="mr-3" />
            <div class="flex items-center gap-x-2">
              <div class="font-semibold text-[15px]">宝宝兔</div>
              <div class="inline-block bg-[#66ccff] text-white text-[9px] rounded-full px-1 py-0">
                控分/8场
              </div>
            </div>
          </div>
          <div class="flex items-center gap-x-2">
            <img width="11" height="13" fit="cover" src="@/assets/live/icon_fire.png" />
            <div class="text-xs font-bold">逸臣</div>
            <div class="text-xs font-bold">980</div>
          </div>
        </div>
        <div class="flex justify-between items-center pl-4 pr-5 py-[17px] border-b last:border-b-0">
          <div class="flex items-center gap-x-2">
            <div class="w-8 text-right font-bold mr-2">07.</div>
            <van-image round width="35" height="35" fit="cover" src="https://randomuser.me/api/portraits/men/15.jpg"
              class="mr-3" />
            <div class="flex items-center gap-x-2">
              <div class="font-semibold text-[15px]">老张爱下棋</div>
              <div class="inline-block bg-[#66ccff] text-white text-[9px] rounded-full px-1 py-0">
                控分/8场
              </div>
            </div>
          </div>
          <div class="flex items-center gap-x-2">
            <img width="11" height="13" fit="cover" src="@/assets/live/icon_fire.png" />
            <div class="text-xs font-bold">逸臣</div>
            <div class="text-xs font-bold">980</div>
          </div>
        </div>
        <div class="flex justify-between items-center pl-4 pr-5 py-[17px] border-b last:border-b-0">
          <div class="flex items-center gap-x-2">
            <div class="w-8 text-right font-bold mr-2">08.</div>
            <van-image round width="35" height="35" fit="cover" src="https://randomuser.me/api/portraits/men/16.jpg"
              class="mr-3" />
            <div class="flex items-center gap-x-2">
              <div class="font-semibold text-[15px]">人生需奋斗</div>
              <div class="inline-block bg-[#66ccff] text-white text-[9px] rounded-full px-1 py-0">
                控分/8场
              </div>
            </div>
          </div>
          <div class="flex items-center gap-x-2">
            <img width="11" height="13" fit="cover" src="@/assets/live/icon_fire.png" />
            <div class="text-xs font-bold">逸臣</div>
            <div class="text-xs font-bold">980</div>
          </div>
        </div>
        <div class="flex justify-between items-center pl-4 pr-5 py-[17px] border-b last:border-b-0">
          <div class="flex items-center gap-x-2">
            <div class="w-8 text-right font-bold mr-2">09.</div>
            <van-image round width="35" height="35" fit="cover" src="https://randomuser.me/api/portraits/men/17.jpg"
              class="mr-3" />
            <div class="flex items-center gap-x-2">
              <div class="font-semibold text-[15px]">创业新星</div>
              <div class="inline-block bg-[#66ccff] text-white text-[9px] rounded-full px-1 py-0">
                控分/8场
              </div>
            </div>
          </div>
          <div class="flex items-center gap-x-2">
            <img width="11" height="13" fit="cover" src="@/assets/live/icon_fire.png" />
            <div class="text-xs font-bold">逸臣</div>
            <div class="text-xs font-bold">980</div>
          </div>
        </div>
        <div class="flex justify-between items-center pl-4 pr-5 py-[17px] border-b last:border-b-0">
          <div class="flex items-center gap-x-2">
            <div class="w-8 text-right font-bold mr-2">10.</div>
            <van-image round width="35" height="35" fit="cover" src="https://randomuser.me/api/portraits/men/18.jpg"
              class="mr-3" />
            <div class="flex items-center gap-x-2">
              <div class="font-semibold text-[15px]">最佳老司机</div>
              <div class="inline-block bg-[#66ccff] text-white text-[9px] rounded-full px-1 py-0">
                控分/8场
              </div>
            </div>
          </div>
          <div class="flex items-center gap-x-2">
            <img width="11" height="13" fit="cover" src="@/assets/live/icon_fire.png" />
            <div class="text-xs font-bold">逸臣</div>
            <div class="text-xs font-bold">980</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>

const props = defineProps({
  type: String,
});
</script>

<style>

</style>
