<template>
  <div class="input-comment-container">
    <div class="template-button-container">
      <div class="template-button" @click="isShowTemplate = !isShowTemplate">
        模板
        <van-icon name="arrow-down" size="12" color="white" />
      </div>
    </div>
    <div class="input-comment-content">
      <div class="input-comment">
        <img src="@/assets/live/chat_icon.png" class="w-3 h-3" />
        <van-field v-model="commentText" type="text" placeholder="说点什么~" :border="false" class="comment-input" />
        <van-button type="primary" class="send-button">发送</van-button>
      </div>
      <div class="tool-options">
        <div>弹幕</div>
        <van-checkbox v-model="checked" checked-color="#1da9f1" />
      </div>
    </div>
    <div v-if="isShowTemplate" class="template-container">
      <div  v-for="template in templateList" :key="template" class="template-item">{{ template }}</div>
    </div>
  </div>
</template>

<script setup>
// Props
defineProps({});

const commentText = ref('');
const checked = ref(false);
const isShowTemplate = ref(false);
const templateList = ref([
  '主播好漂亮！！',
  '小姐姐操作好厉害！！',
  '主播真可爱',
  '小姐姐笑起来真甜！！',
  '小姐姐操作真六，膜拜膜拜！',
  '美女主播666！！',
  '主播真可爱',
  '小姐姐笑起来真甜！！',
  '小姐姐操作真六，膜拜膜拜！',
  '美女主播666！！',
  '主播真可爱',
  '小姐姐笑起来真甜！！'
]);
</script>

<style scoped>
.input-comment-container {
  position: relative;
}

.template-button-container {
  position: absolute;
  display: flex;
  justify-content: right;
  padding: 5px;
  overflow: auto;
  top: 0;
  right: 0;
  transform: translateY(-100%);
}

.template-button {
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  border-radius: 10px;
  padding: 2px 10px;
  font-size: 12px;
  height: 20px;
}

.input-comment-content {
  padding: 7px;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  gap: 10px;
  align-items: center;
  justify-content: space-between;
}

.input-comment {
  display: flex;
  flex: 1;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 5px;
  bottom: 10px;
  left: 10px;
  right: 10px;
  border: solid 1px #1ea8f1;
}

.comment-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
}

.comment-input {
  flex: 1;
  background: transparent;
  color: #999999;
  resize: none;
  padding: 0;
  font-size: 12px;
}

.send-button {
  background: #1ea8f1;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 2px 10px;
  font-size: 12px;
  height: 20px;
}

.tool-options {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox {
}

.template-container {
  padding: 10px;
  background: rgba(0, 0, 0, 0.5);
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
  display: flex;
  flex-wrap: wrap;
  gap: 7px;
}

.template-item {
  padding: 10px;
  border-radius: 30px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  margin-bottom: 5px;
  cursor: pointer;
  font-size: 12px;
}
</style>
