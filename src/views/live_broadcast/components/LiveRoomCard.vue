<template>
  <div class="flex flex-col">
    <!-- Main card -->
    <div
      class="relative rounded-xl overflow-hidden bg-white/90 aspect-[88/107] rounded-b-none"
    >
      <van-image
        :src="avatar"
        class="object-cover block w-full h-full"
      />
      <div
        v-if="title"
        class="text-[12px] text-white absolute left-0 top-0 w-[70px] h-[24px] bg-black/75 pl-[24px] rounded-br-full flex items-center"
      >
        {{ title }}
      </div>
      <div
        v-if="desc"
        class="text-[12px] text-white absolute left-0 bottom-0 w-full min-h-[100px] bg-gradient-to-t from-black/75 to-transparent shadow-lg p-2 flex justify-center items-end truncate"
      >
        {{ desc }}
      </div>
      <div
        v-if="tags && tags.length > 0"
        class="absolute top-[3px] right-[3px] flex items-center"
      >
        <div v-for="tag in tags" class="px-1 py-0.5 text-[9px] text-[#ffd631] font-bold bg-black/75 rounded-full mr-1">
          {{ tag.text }}
        </div>
      </div>
      <div class="absolute top-[26px] left-[3px] space-y-[3px] flex flex-col">
        <img v-if="iconPk" class="w-[24px]" src="@/assets/live/icon_pk.png" alt="iconPk" />
        <img v-if="iconPhone" class="w-[24px]" src="@/assets/live/icon_phone.png" alt="iconPhone" />
      </div>
    </div>
    
    <!-- Footer -->
    <div class="flex items-center justify-between p-2 bg-[#292929] rounded-b-xl">
      <div class="flex items-center">
        <div class="w-6 h-6 rounded-full  overflow-hidden mr-2">
          <van-image
            :src="hostAvatar || avatar"
            class="object-cover block w-full h-full"
          />
        </div>
        <span class="text-white text-xs truncate max-w-[80px]">{{ hostName }}</span>
      </div>
      <div class="flex items-center">
        <img src="@/assets/live/icon_fire.png" class="w-3 h-3 mr-1" alt="viewers" />
        <span class="text-[#ffd631] text-xs font-bold">{{ viewerCount }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue';
const props = defineProps({
  avatar: { type: String, required: true },
  title: { type: String, required: false, default: '' },
  desc: { type: String, required: false, default: '' },
  tags: { type: Array, default: () => [] }, // [{ text }]
  iconPk: { type: String, default: '' },
  iconPhone: { type: String, default: '' },
  hostAvatar: { type: String, default: '' },
  hostName: { type: String, default: '主播' },
  viewerCount: { type: String, default: '0' },
});
</script>

<style scoped></style>
