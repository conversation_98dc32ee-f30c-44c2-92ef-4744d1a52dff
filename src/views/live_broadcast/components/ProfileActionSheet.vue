<template>
  <van-action-sheet
    v-model:show="localShow"
    :style="{ height: '70vh' }"
    :close-on-click-overlay="true"
    round
    teleport="body"
    closeable
    class="profile-action-sheet"
    style="overflow: visible;"
  >
    <div class="bg-white h-full flex flex-col relative font-[var(--van-base-font)] overflow-y-visible">
      <!-- Report button -->
      <div class="absolute top-4 left-4 z-10">
        <button class="bg-pink-100 text-pink-600 px-3 py-1 rounded-full text-sm font-medium border-none">
          举报
        </button>
      </div>

      <!-- Content -->
      <div class="flex-1 flex flex-col items-center px-6 relative z-10">
        <!-- Avatar -->
        <div class="mb-4 relative -mt-10">
          <img 
            :src="profileData.avatar" 
            :alt="profileData.name"
            class="w-24 h-24 object-cover border-8 border-white border-solid rounded-full"
          />
        </div>

        <!-- Name and Follow Button -->
        <div class="flex items-center space-x-3">
          <h2 class="text-2xl font-bold text-gray-900">{{ profileData.name }}</h2>
          <button class="bg-transparent text-white w-8 h-8 rounded-full flex items-center justify-center border-none">
            <img class="block" width="32" height="32" src="@/assets/live/icon-heart.png" fit="cover" />
          </button>
        </div>
        <!-- Host ID -->
        <p class="text-gray-500 text-sm mb-8">主播ID:{{ profileData.hostId }}</p>

        <!-- Contribution List -->
        <div class="w-full max-w-sm mb-8">
          <div class="bg-white px-4 py-2 rounded-full shadow-[0px_0px_54px_0px_rgba(166,189,198,0.32)]">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <!-- Chart icon placeholder -->
                <img class="block" width="15" height="15" src="@/assets/live/<EMAIL>" fit="cover" />
                <span class="text-gray-700 font-medium">个人贡献榜</span>
              </div>
              
              <!-- Contribution avatars stack -->
              <div class="flex items-center">
                <div 
                  v-for="(contributor, index) in contributionList" 
                  :key="index"
                  class="relative -ml-2"
                >
                  <img 
                    :src="contributor.avatar" 
                    :alt="contributor.name"
                    class="w-10 h-10 rounded-full object-cover border-2 border-solid border-[var(--van-primary-color)] shadow-sm"
                  />
                </div>
              </div>

              <img class="block" width="10" height="10" src="@/assets/webp/icon/icon-arrow.webp" fit="contain" />
            </div>
          </div>
        </div>

        <!-- Main Button -->
        <button 
          class="px-10 py-2 bg-[var(--van-primary-color)] text-white rounded-full border-none"
          @click="goToIdolDetail"
        >
          主页
        </button>
      </div>
    </div>
  </van-action-sheet>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useRouter } from 'vue-router';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:show']);

const router = useRouter();

const localShow = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

// Profile data
const profileData = ref({
  name: '小纯御',
  hostId: '4113',
  avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80'
});

// Contribution list data
const contributionList = ref([
  {
    name: '贡献者1',
    avatar: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '贡献者2',
    avatar: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '贡献者3',
    avatar: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '贡献者4',
    avatar: 'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?auto=format&fit=facearea&w=256&h=256&q=80'
  }
]);

const goToIdolDetail = () => {
  router.push(`/live_broadcast/idol_detail/${profileData.value.hostId}`);
};
</script>

<style>
.profile-action-sheet .van-action-sheet__content {
  overflow: visible;
}
</style>