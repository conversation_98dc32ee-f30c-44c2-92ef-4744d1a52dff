<template>
  <div class="bottom-action-bar">
    <!-- Input Area -->
    <div class="comment-input" @click="handleCommentInput">
      <img src="@/assets/live/chat_icon.png" class="w-3 h-3" />
      <div class="text-placeholder">说点什么~</div>
    </div>

    <!-- Action Icons -->
    <div class="action-icons">
      <div v-for="action in actionsList" :key="action.name" class="icon-item" @click="action.onClick">
        <img :src="action.icon" class="h-full" />
      </div>
    </div>
  </div>
</template>

<script setup>
import action1 from '@/assets/live/action_1.png';
import action2 from '@/assets/live/action_2.png';
import action3 from '@/assets/live/action_3.png';
import action4 from '@/assets/live/action_4.png';
import action5 from '@/assets/live/action_5.png';
import action6 from '@/assets/live/action_6.png';

// Props
defineProps({
  commentText: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['commentInput', 'iconAction', 'vipCharge', 'settings']);

const actionsList = [
  {
    icon: action1,
    name: '1',
    onClick: () => handleIconAction('action1')
  },
  {
    icon: action2,
    name: '2',
    onClick: () => handleIconAction('action2')
  },
  {
    icon: action3,
    name: '3',
    onClick: () => handleIconAction('action3')
  },
  {
    icon: action4,
    name: '4',
    onClick: () => handleIconAction('action4')
  },
  {
    icon: action5,
    name: '5',
    onClick: () => handleIconAction('action5')
  },
  {
    icon: action6,
    name: 'settings',
    onClick: () => handleSettings()
  }
];

// Event handlers
const handleCommentInput = () => {
  console.log('comment input');
  emit('commentInput');
};

const handleIconAction = (action) => {
  emit('iconAction', action);
};

const handleVipCharge = () => {
  emit('vipCharge');
};

const handleSettings = () => {
  emit('settings');
};
</script>

<style scoped>
/* Bottom Action Bar */
.bottom-action-bar {
  display: flex;
  align-items: center;
  gap: 4px;
  /* height: 60px; */
  padding: 4px 4px;
  padding-bottom: 10px;
}

.comment-input {
  flex: 1;
  background: rgba(0, 0, 0, 0.3);
  border-radius: 5px;
  padding: 4px 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.text-placeholder {
  color: #fff;
  opacity: 0.62;
  font-size: 12px;
  text-break: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.action-icons {
  display: flex;
  gap: 8px;
  height: 30px;
  padding-left: 8px;
  padding-right: 8px;
}

.icon-item {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
