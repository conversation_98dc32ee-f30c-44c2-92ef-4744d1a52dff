<template>
  <div class="host-info-container">
    <div class="host-info-content">
      <!-- Host Avatar with Badge -->
      <div class="avatar-container">
        <img :src="userData.data.data.user_info.avatar" alt="Host Avatar" class="host-avatar" />
      </div>
      <div class="host-info">
        <div>
          <span class="host-name">{{ userData.data.data.user_info.nickname }}</span>
        </div>
        <div class="host-status">
          <span>关注主播不迷路 </span>
          <img src="@/assets/live/icon_c03.png" alt="Host Status" class="host-status-icon" />
        </div>
      </div>
      <div class="heart-icon-container">
        <img src="@/assets/live/blue_heart_plus.png" alt="Host Status" class="heart-icon" />
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  userData: {
    type: Object,
    default: () => ({
      id: '3',
      type: 'user',
      data: {
        data: {
          type: 1,
          content: '李大富 在一分钟一口气进了',
          user_info: {
            nickname: '李大富',
            level: 8,
            levelIcon: '',
            avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=100&h=100&fit=crop&crop=face'
          }
        }
      },
      timestamp: Date.now()
    })
  }
});

const emit = defineEmits([]);
</script>

<style scoped>
.host-info-container {
  display: flex;
  align-items: center;
  margin: 8px 0;
  position: relative;
}

.host-info-content {
  background: #fff;
  padding: 4px;
  border-top-right-radius: 20px;
  border-bottom-right-radius: 20px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.avatar-container {
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid #0ea4f1;
  height: 60px;
  width: 60px;
}

.host-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  height: 100%;
  justify-content: space-between;
}

.host-name {
  font-size: 18px;
  font-weight: 600;
  color: #000;
}

.host-status {
  font-size: 12px;
  color: #000;
  opacity: 0.95;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 4px;
}

.heart-icon-container {
  position: relative;
  flex-shrink: 0;
  padding: 20px; 
}

.heart-icon {
  width: auto;
  height: 30px;
  object-fit: contain;
}
</style>
