<template>
  <van-tabs
    v-model="activeTab"
    color="#00eaff"
    line-width="20"
    title-active-color="#00eaff"
    title-inactive-color="white"
    background="transparent"
    shrink
    :style="{ height: `${props.height}px` }"
  >
    <van-tab title="游戏活动">
      <div class="tab-content" :style="{ height: `${props.height - 70}px` }"><ActivityTab /></div>
    </van-tab>
    <van-tab title="节日活动"
      ><div class="tab-content" :style="{ height: `${props.height - 70}px` }"><ActivityTab /></div
    ></van-tab>
    <van-tab title="推广活动"
      ><div class="tab-content" :style="{ height: `${props.height - 70}px` }"><ActivityTab /></div
    ></van-tab>
    <van-tab title="直播活动"
      ><div class="tab-content" :style="{ height: `${props.height - 70}px` }"><ActivityTab /></div
    ></van-tab>
  </van-tabs>
</template>

<script setup>
import ActivityTab from './ActivityTab.vue';

const props = defineProps({
  height: {
    type: Number,
    default: 270
  }
});

const activeTab = ref(0);
</script>

<style scoped>
.tab-content {
  overflow-y: scroll;
  padding: 8px 12px;
}
</style>
