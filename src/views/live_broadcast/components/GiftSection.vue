<template>
  <div class="gift-section">
    <div class="gift-heart">
      <van-icon name="like" size="20" color="#ff69b4" />
      <span class="gift-text">礼物心愿</span>
      <span class="gift-count">/{{ giftCount }}</span>
    </div>
    <div class="recommendation">
      <span>为你推荐</span>
      <van-icon name="arrow" size="12" />
    </div>
  </div>
</template>

<script setup>
// Props
defineProps({
  giftCount: {
    type: Number,
    default: 61
  }
})
</script>

<style scoped>
/* Gift Section */
.gift-section {
  position: absolute;
  right: 16px;
  top: 120px;
  display: flex;
  flex-direction: column;
  gap: 4px;
  align-items: flex-end;
}

.gift-heart {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.3);
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
}

.gift-text {
  color: white;
}

.gift-count {
  color: rgba(255, 255, 255, 0.7);
}

.recommendation {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  background: rgba(0, 0, 0, 0.3);
  padding: 6px 12px;
  border-radius: 16px;
}
</style> 