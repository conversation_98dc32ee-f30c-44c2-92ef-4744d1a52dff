<template>
  <div :style="{ height: `${props.height}px` }" class="game-content-container">
    <van-tabs
      v-model="activeTab"
      color="#00eaff"
      line-width="20"
      title-active-color="#00eaff"
      title-inactive-color="white"
      background="transparent"
      shrink
    >
      <van-tab title="礼物">
        <div class="tab-content" :style="{ height: `${props.height - 120}px` }"><GiftTab /></div>
      </van-tab>
      <van-tab title="跳蛋"
        ><div class="tab-content" :style="{ height: `${props.height - 120}px` }"><GiftTab /></div
      ></van-tab>
      <van-tab title="特权"
        ><div class="tab-content" :style="{ height: `${props.height - 120}px` }"><GiftTab /></div
      ></van-tab>
      <van-tab title="背包"
        ><div class="tab-content" :style="{ height: `${props.height - 120}px` }"><GiftTab /></div
      ></van-tab>
    </van-tabs>
    <div class="game-info">
      <div class="balance-info">
        <div class="diamond">
          <img class="diamond-icon" src="@/assets/live/diamond_icon.png" />
          <div class="diamond-text">9999</div>
        </div>
        <div class="money">
          <img class="balance-icon" src="@/assets/live/balance_icon.png" />
          <div class="balance-text">280000</div>
          <img class="reload-icon" src="@/assets/live/reload_icon.png" />
        </div>
      </div>
      <div class="game-actions">
        <van-button class="exchange-btn button" size="small" type="primary">
          兑换钻石
          <van-icon name="arrow" size="12" />
        </van-button>
        <div class="send-btn" size="small" type="primary">
          <van-popover v-model:show="showPopover" placement="top" :offset="[0, 40]">
            <div class="dropdown-menu">
              <div class="dropdown-item" v-for="item in dropdownItems" :key="item.value" @click="onSelect(item)">
                <div class="dropdown-item-value">
                  {{ item.value }}
                </div>
                <div class="dropdown-item-text">{{ item.text }}</div>
              </div>
            </div>
            <template #reference>
              <div class="dropdown-title">
                {{selectedValue}}
                <van-icon name="arrow-down" size="12" />
              </div>
            </template>
          </van-popover>
          <div class="send-text">赠送</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import GiftTab from './GiftTab.vue';

const props = defineProps({
  height: {
    type: Number,
    default: 270
  }
});

const dropdownItems = [
  {
    value: 1314,
    text: '一生一世'
  },
  {
    value: 520,
    text: '我爱你'
  },
  {
    value: 188,
    text: '要抱抱'
  },
  {
    value: 66,
    text: '六六大顺'
  },
  {
    value: 30,
    text: '想你'
  },
  {
    value: 10,
    text: '十全十美'
  },
  {
    value: 1,
    text: '一心一意'
  }
];

const activeTab = ref(0);
const showPopover = ref(false);
const selectedValue = ref(1);

const onSelect = (action: any) => {
  selectedValue.value = action.value;
  showPopover.value = false;
};
</script>

<style scoped>
.game-content-container {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.tab-content {
  overflow-y: scroll;
  padding: 8px 12px;
}

.game-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px;
}

.balance-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.diamond {
  display: flex;
  align-items: center;
  gap: 8px;
}

.diamond-icon {
  width: 15px;
  height: 15px;
}

.diamond-text {
  color: white;
  font-size: 13px;
  font-weight: bold;
}

.money {
  display: flex;
  align-items: center;
  gap: 8px;
}

.balance-icon {
  width: 15px;
  height: 15px;
}

.balance-text {
  color: white;
  font-size: 13px;
  font-weight: bold;
  color: #ff9c00;
}

.reload-icon {
  width: 11px;
  height: 11px;
}

.game-actions {
  display: flex;
  gap: 8px;
  height: 100%;
}

.button {
  border-radius: 20px;
  padding: 2px 10px;
  font-size: 12px;
  height: 100%;
  min-width: 100px;
  color: white;
  border: 1px solid #11cfff;
  background-color: transparent;
}

.exchange-btn {
  color: #11cfff;
  font-weight: bold;
}

.send-btn {
  border: 1px solid #11cfff;
  background-color: transparent;
  border-radius: 20px;
  font-size: 12px;
  min-width: 120px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  overflow: hidden;
}

.dropdown-title {
  color: white;
  font-size: 12px;
  font-weight: bold;
  text-align: center;
  width: 60px;
}

.dropdown-menu {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 8px 0;
  width: 100px;
}

.dropdown-item {
  cursor: pointer;
  font-size: 12px;
  font-weight: bold;
  padding: 5px 10px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 4px;
}

.dropdown-item:hover {
  background-color: #f2f2f2;
}

.dropdown-item-value {
  color: black;
}

.dropdown-item-text {
  color: #ddadc9;
  font-weight: bold;
}

.send-text {
  cursor: pointer;
  background-color: #009dc5;
  height: 100%;
  width: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
  font-weight: bold;
}
</style>
