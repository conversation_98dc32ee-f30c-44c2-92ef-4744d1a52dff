<template>
  <div class="floating-timer">
    <div class="timer-badge">
      <van-icon name="clock" size="12" color="white" />
      <span>{{ liveTimer }}</span>
    </div>
  </div>
</template>

<script setup>
// Props
defineProps({
  liveTimer: {
    type: String,
    default: '00:00'
  }
})
</script>

<style scoped>
/* Floating Timer */
.floating-timer {
  position: absolute;
  top: 70px;
  right: 16px;
}

.timer-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(0, 0, 0, 0.7);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
}
</style> 