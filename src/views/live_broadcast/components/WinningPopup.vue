<template>
  <div class="winning-popup-container">
    <div class="background">
      <img src="@/assets/live/winning_bg.png" alt="New User" />
    </div>
    <div class="winning-popup-content-container">
      <div class="winning-message">
        <span>恭喜</span>
        <span class="text-[#9fe3ff]">{{ usename }}</span>
        <span>在</span>
        <span class="text-[#fff109]">{{ gameName }}</span>
        <span>中赢了</span>
        <span class="text-[#fff109]">{{ winningAmount }}</span>
        <span>元</span>
      </div>
      <div class="winning-icon-container" @click="onPlay">
        <img src="@/assets/live/winning_button.png" alt="New User" class="winning-icon" />
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  usename: {
    type: String,
    default: '伊索言喻'
  },
  gameName: {
    type: String,
    default: '一分六合彩'
  },
  winningAmount: {
    type: Number,
    default: 18600
  }
});

const emit = defineEmits(['onPlay']);

const onPlay = () => {
  emit('onPlay');
};
</script>

<style scoped>
.winning-popup-container {
  display: flex;
  align-items: center;
  margin: 8px 0;
  position: relative;
}

.winning-popup-content-container {
  position: absolute;
  inset: 0;
  left: 40px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 4px;
  padding-right: 4px;
  border-radius: 20px;
  gap: 10px;
}

.winning-message {
  font-size: 12px;
  color: #ffffff;
  opacity: 0.95;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  font-weight: 500;
}

.winning-icon-container {
  position: relative;
  flex-shrink: 0;
  cursor: pointer;
}

.winning-icon {
  width: auto;
  height: 22px;
  object-fit: contain;
}
</style>
