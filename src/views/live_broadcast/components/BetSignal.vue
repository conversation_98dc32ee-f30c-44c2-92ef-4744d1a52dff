<template>
  <div class="container">
    <img src="@/assets/live/sin_icon.png" alt="Bet Signal" class="sin-icon" />
    <img src="@/assets/live/signal_strength.png" alt="User Avatar" class="signal-strength" />
    <div class="signal-text">
      抚子姐姐
    </div>
    <div class="timmer">
      <span class="time">20</span>
      <span class="unit">s</span>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({});

const emit = defineEmits([]);
</script>

<style scoped>
.container {
  display: flex;
  align-items: center;
  gap: 20px;
  margin: 8px 0;
  position: relative;
  padding-left: 10px;
}

.sin-icon {
  width: auto;
  height: 22px;
  object-fit: contain;
}

.signal-strength {
  width: auto;
  height: 22px;
  object-fit: contain;
}

.signal-text {
  font-size: 16px;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  font-weight: bold;
}

.timer {
  position: absolute;
  right: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.time {
  font-size: 16px;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  font-weight: bold;
}

.unit {
  font-size: 16px;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}
</style>
