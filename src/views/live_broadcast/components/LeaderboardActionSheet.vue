<template>
  <van-action-sheet v-model:show="visible" color="#000" :closeable="true" close-icon="cross" :style="{
    '--van-popup-background': '#fff',
    '--van-action-sheet-item-text-color': '#000',
    'height': '400px'
  }">
    <div
      class="relative min-h-screen pt-8 bg-contain md:bg-cover bg-no-repeat bg-top-left bg-ranking">
      <img src="@/assets/live/skin_bg_ranking.jpg" class="absolute top-0 left-0 w-full h-full object-contain object-top" />
      <van-tabs v-model:active="activeTab" class="custom-tabs" animated>
        <van-tab title="日榜">
          <RankingList :type="'day'" />
        </van-tab>
        <van-tab title="周榜">
          <RankingList :type="'week'" />
        </van-tab>
        <van-tab title="月榜">
          <RankingList :type="'month'" />
        </van-tab>
      </van-tabs>
    </div>
  </van-action-sheet>
</template>

<script setup>

const activeTab = ref(0);
const onClickLeft = () => history.back();
import RankingList from './RankingList.vue';
defineOptions({
  name: 'LeaderboardActionSheet'
})

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:show', 'action'])

const visible = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
})

const handleAction = () => {
  emit('action', 'leaderboard-test', { message: 'Leaderboard action triggered' })
}
</script>

<style>
.custom-tabs .van-tabs__wrap {
  background: transparent;
  border-bottom: none;
}

.custom-tabs .van-tab {
  font-size: 15px;
  color: #fff;
  border-radius: 9999px;
  transition: all 0.2s;
}

.custom-tabs .van-tab--active {
  background: #fff;
  color: #1da9f1;
  border-radius: 9999px;
}

.custom-tabs .van-tabs__nav {
  background: #3366cc;
  border-radius: 9999px;
  padding: 0;
  display: flex;
  justify-content: space-between;
}

.custom-tabs .van-tab .van-tab__text {
  font-weight: 500;
}

.custom-tabs .van-tab--active .van-tab__text {
  font-weight: 700;
}

.custom-tabs .van-tabs__wrap {
  padding: 0 15px;
  height: 34px;
}

.custom-tabs .van-tabs__line {
  display: none;
}
</style>