<template>
  <div class="bg-white h-full flex flex-col">
    <!-- Header with <PERSON><PERSON> -->
    <div class="flex justify-between items-center p-4">
      <div class="w-[35px] h-[35px]"></div>
      <div class="text-[#333333] text-[18px] font-bold">开通守护</div>
      <img class="w-[35px] h-[35px]" src="@/assets/live/question_mark_icon.png" />
    </div>
    <div class="flex flex-row px-1">
      <div
        v-for="(guardian, index) in guardianList"
        :key="index"
        :class="{
          'cursor-pointer relative p-1 border border-transparent rounded-3xl border-solid border-3': true,
          '!border-[#28b5fd]': index === selectedGuardian
        }"
        @click="selectedGuardian = index"
      >
        <img :src="guardian.bgImage" class="w-full" />
        <div class="absolute inset-0 flex flex-col items-center justify-space-between py-6">
          <img :src="guardian.icon" class="w-[90%]" />
          <div class="flex-1"></div>
          <div class="flex flex-col items-center gap-2">
            <div class="text-white font-bold">{{ guardian.name }}</div>
            <div class="flex items-center gap-1">
              <img src="@/assets/live/diamond_icon.png" class="w-[18px]" />
              <span class="text-white font-bold">{{ guardian.price }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div>
      <div class="p-4 flex items-center gap-2">
        <div class="bg-[#00ccff] h-[20px] w-[5px] rounded"></div>
        <div class="text-[#333333] text-[15px] font-bold">为主播开通守护</div>
      </div>
      <div class="grid grid-cols-4 gap-4 px-4 py-2">
        <div
          v-for="feature in featureList"
          :key="feature.id"
          :class="{
            'flex flex-col items-center gap-2': true,
            grayscale: !guardianList[selectedGuardian].featureList.includes(feature.id)
          }"
        >
          <img :src="feature.icon" class="w-full" />
          <div class="text-[#333333] text-[13px] font-bold">{{ feature.name }}</div>
        </div>
      </div>
    </div>
    <div class="p-4 flex gap-4 items-center justify-between">
      <div class="p-3 flex items-center gap-6 bg-[#f0f0f0] rounded-3xl w-fit">
        <div class="flex items-center gap-1">
          <img src="@/assets/live/diamond_icon.png" class="w-[18px]" />
          <span class="text-[#333333] text-[13px] font-bold">9999</span>
        </div>

        <div class="flex items-center gap-1 pr-4">
          <img src="@/assets/live/balance_icon.png" class="w-[18px]" />
          <span class="text-[#333333] text-[13px] font-bold">9999</span>
        </div>
      </div>
      <img src="@/assets/live/enable_guardian_btn.png" class="h-[43px] cursor-pointer" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { getRankIcon } from '@/utils/vipLv';

const selectedGuardian = ref(0);

const guardianList = ref([
  {
    name: '周守护',
    bgImage: '/src/assets/live/guardian_bg_1.png',
    icon: 'src/assets/live/guardian_icon_1.png',
    price: 999,
    featureList: [1, 2]
  },
  {
    name: '月守护',
    bgImage: 'src/assets/live/guardian_bg_2.png',
    icon: 'src/assets/live/guardian_icon_2.png',
    price: 3333,
    featureList: [1, 2, 3]
  },
  {
    name: '真爱年守护',
    bgImage: 'src/assets/live/guardian_bg_3.png',
    icon: 'src/assets/live/guardian_icon_3.png',
    price: 33333,
    featureList: [1, 2, 3, 4]
  }
]);

const featureList = ref([
  {
    id: 1,
    name: '身份识别',
    icon: 'src/assets/live/guardian_feature_1.png'
  },
  {
    id: 2,
    name: '进场特效',
    icon: 'src/assets/live/guardian_feature_2.png'
  },
  {
    id: 3,
    name: '专属礼物',
    icon: 'src/assets/live/guardian_feature_3.png'
  },
  {
    id: 4,
    name: '防踢防禁言',
    icon: 'src/assets/live/guardian_feature_4.png'
  }
]);
</script>

<style scoped>

</style>
