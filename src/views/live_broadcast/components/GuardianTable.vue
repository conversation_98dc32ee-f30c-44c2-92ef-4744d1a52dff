<template>
  <div class="pt-4 pb-0 w-full">
    <table class="w-full border-collapse">
      <thead class="bg-[#eeeeee]">
        <tr class="text-[#333333] text-[12px] font-bold text-left">
          <th class="py-1 pl-4">周排名</th>
          <th class="p-0">昵称</th>
          <th class="p-0">守护天数</th>
          <th class="p-0">贡献值</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in list" :key="index" class="text-[#333333] text-[13px] font-bold text-left">
          <td class="h-[35px] py-1 pl-4">
            <img v-if="index === 0" src="@/assets/live/no1.png" class="h-[25px]" />
            <img v-else-if="index === 1" src="@/assets/live/no2.png" class="h-[25px]" />
            <img v-else-if="index === 2" src="@/assets/live/no3.png" class="h-[25px]" />
            <span v-else>NO.{{ index + 1 }}</span>
          </td>
          <td class="flex items-center gap-2 h-[35px] py-1">
            <div class="relative">
              <img :src="item.avatar" class="w-[25px] rounded-full border-[3px] border-solid border-[#993ee8]" />
              <img :src="item.rankIcon" class="absolute bottom-[-5px] left-1/2 -translate-x-1/2 w-[25px]" />
            </div>
            <div class="text-[13px] font-bold">
              {{ item.name }}
            </div>
          </td>
          <td class="h-[35px] py-1">
            <div class="text-[13px] text-[#18c8ad] font-bold">
              {{ item.guardDay }}
            </div>
          </td>
          <td class="h-[35px] py-1">
            <div class="text-[13px] text-[#ff9c00] font-bold">
              {{ item.amount }}
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <div v-if="list.length === 0" class="pt-8 pb-12 flex flex-col items-center">
      <img src="@/assets/live/sad_emoj.png" class="w-[60px] h-[60px]" />
      <div class="mt-4 text-[14px] text-[#333333] font-bold">这位小仙女暂时没有守护哦~</div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  list: {
    type: Array,
    default: () => []
  }
});
</script>

<style scoped></style>
