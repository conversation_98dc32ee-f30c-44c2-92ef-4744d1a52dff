<template>
  <div class="right-action-bar">
    <!-- Action Icons -->
    <div class="action-icons">
      <div v-for="action in actionsList" :key="action.name" class="icon-item" @click="handleIconAction('share')">
        <img :src="action.icon" class="h-full" />
      </div>
    </div>
  </div>
</template>

<script setup>
import action1 from '@/assets/live/action_1.png';
import action2 from '@/assets/live/action_2.png';
import action3 from '@/assets/live/action_3.png';

// Props
defineProps({
  commentText: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['update:commentText', 'iconAction', 'vipCharge', 'settings']);

const actionsList = [
  {
    icon: action1,
    name: '1',
    onClick: () => {}
  },
  {
    icon: action2,
    name: '2',
    onClick: () => {}
  },
  {
    icon: action3,
    name: '3',
    onClick: () => {}
  },
];

// Event handlers
const updateCommentText = (value) => {
  emit('update:commentText', value);
};

const handleIconAction = (action) => {
  emit('iconAction', action);
};

const handleVipCharge = () => {
  emit('vipCharge');
};

const handleSettings = () => {
  emit('settings');
};
</script>

<style scoped>
/* Bottom Action Bar */
.right-action-bar {
  position: absolute;
  bottom: 50px;
  right: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 4px;
  padding-bottom: 10px;
}

.action-icons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 40px;
  padding-left: 8px;
  padding-right: 8px;
}

.icon-item {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
