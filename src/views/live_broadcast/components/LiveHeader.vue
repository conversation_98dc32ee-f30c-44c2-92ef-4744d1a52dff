<template>
  <div class="flex justify-between items-start pl-2.5 pr-[6px] fixed left-0 right-0 top-6 z-10">
    <div class="flex items-start flex-col gap-1.5">
      <div class="flex items-center gap-1 px-0.5 py-0.5 bg-black/50 rounded-full">
        <van-image 
          src="/static/webp/avatar8.webp" 
          round 
          fit="cover" 
          width="32" 
          height="32" 
          @click="openProfile"
          class="cursor-pointer" 
        />
        <div class="text-white text-[10px] cursor-pointer" @click="openProfile">小纯御</div>
        <van-button 
          round 
          type="primary" 
          class="mx-1 bg-[#66ccff] text-white p-0 w-[40px]"
          style="--van-button-mini-font-size: 8px;" 
          size="mini"
        >
          关注
        </van-button>
      </div>
      
      <div class="flex items-center gap-x-[5px]">
        <button
          class="flex flex-col justify-center items-center w-7 h-7 rounded bg-black/50 cursor-pointer ring-0 outline-none border-none"
          style="--van-button-mini-font-size: 8px;" 
          @click="openLeaderboard"
        >
          <img src="@/assets/live/icon_c01.png" width="12" height="13" />
          <div class="text-[8px] leading-3 text-white">贡献榜</div>
        </button>
        
        <button
          class="flex flex-col justify-center items-center w-7 h-7 rounded bg-black/50 cursor-pointer ring-0 outline-none border-none"
          @click="openVipMembers"
        >
          <img src="@/assets/live/icon_c02.png" width="20" height="13" />
          <div class="text-[8px] leading-3 text-white">108人</div>
        </button>
        
        <button
          class="flex flex-col justify-center items-center w-7 h-7 rounded bg-black/50 cursor-pointer ring-0 outline-none border-none"
          @click="openWatchList"
        >
          <img src="@/assets/live/icon_c03.png" width="17" height="13" />
          <div class="text-[8px] leading-3 text-white">观看</div>
        </button>
      </div>
      
      <!-- TinsLottery Integration -->
      <div class="interactive-section">
        <TinsLottery 
          :gid="gameId"
          :res="gameResult"
          :animated="gameAnimated"
          size="small"
        />
      </div>
    </div>
    
    <div class="flex gap-x-[5px] items-center">
      <div class="relative mr-[5px]">
        <img class="block size-8 rounded-full overflow-hidden" src="/static/webp/avatar8.webp" fit="cover" />
        <div class="absolute bottom-[8%] -left-[15%] w-[130%] h-[130%]">
          <img class="block" src="@/assets/live/icon_ring_01.png" fit="cover" />
        </div>
      </div>
      
      <div
        class="w-[30px] h-[30px] flex items-center justify-center rounded-full bg-black/50 text-white text-[10px] font-bold">
        <img src="@/assets/live/icon_mask_01.png" width="26" height="9" />
      </div>

      <div class="relative">
        <img class="block size-8 rounded-full overflow-hidden" src="/static/webp/avatar8.webp" fit="cover" />
        <div
          class="absolute w-full h-full flex items-end justify-center left-0 bottom-0 rounded-full text-white text-[8px] bg-black/20">
          532
        </div>
      </div>

      <div class="relative">
        <img class="block size-8 rounded-full overflow-hidden" src="/static/webp/avatar8.webp" fit="cover" />
        <div
          class="absolute w-full h-full flex items-end justify-center left-0 bottom-0 rounded-full text-white text-[8px] bg-black/20">
          58
        </div>
      </div>

      <button 
        @click="openWatchList"
        class="w-[30px] h-[30px] flex items-center justify-center rounded-full bg-black/50 text-white text-[10px] font-bold ring-0 outline-none border-none"
      >
        12.1k
      </button>

      <button
        class="close-btn w-[30px] h-[30px] flex items-center justify-center rounded-full bg-black/50 ring-0 outline-none border-none">
        <img src="@/assets/live/icon_x_white.png" width="13" height="13" />
      </button>
    </div>
  </div>
</template>

<script setup>
import TinsLottery from '@/views/game_lottery/components/TinsLottery'

// Props
defineProps({
  gameId: {
    type: String,
    default: '1'
  },
  gameResult: {
    type: String,
    default: '123'
  },
  gameAnimated: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['openProfile', 'openLeaderboard', 'openWatchList', 'openVipMembers'])

// Event handlers
const openProfile = () => {
  emit('openProfile')
}

const openLeaderboard = () => {
  emit('openLeaderboard')
}

const openWatchList = () => {
  emit('openWatchList')
}

const openVipMembers = () => {
  emit('openVipMembers')
}
</script>

<style scoped>
/* Interactive Section */
.interactive-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style> 