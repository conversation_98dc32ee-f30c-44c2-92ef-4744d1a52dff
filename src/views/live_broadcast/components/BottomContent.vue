<template>
  <div class="bottom-content-container">
    <div class="tool-bar">
      <div class="chat-icon">
        <img src="@/assets/live/action_7.png" class="tool-icon" />
      </div>
      <div class="other-icons">
        <img src="@/assets/live/action_5.png" class="tool-icon" @click="handleToolChange('gift')" />
        <img src="@/assets/live/action_8.png" class="tool-icon" @click="handleToolChange('game')" />
        <img src="@/assets/live/action_3.png" class="tool-icon" @click="handleToolChange('activity')" />
        <img src="@/assets/live/action_9.png" class="tool-icon" @click="handleToolChange('share')" />
        <img src="@/assets/live/close_icon.png" @click="handleClose" class="tool-icon" />
      </div>
    </div>
    <div class="content">
      <GiftContent v-if="activeTool === 'gift'" :height="props.height" />
      <GameContent v-if="activeTool === 'game'" :height="props.height" />
      <ActivityContent v-if="activeTool === 'activity'" :height="props.height" />
      <ActivityContent v-if="activeTool === 'share'" :height="props.height" />
    </div>
  </div>
</template>

<script setup>
import ActivityContent from './ActivityContent.vue';
import GameContent from './GameContent.vue';
import GiftContent from './GiftContent.vue';

const props = defineProps({
  // gift, game, activity, share
  activeTool: {
    type: String,
    default: 'game'
  },
  height: {
    type: Number,
    default: 320
  }
});

const emit = defineEmits(['close', 'changeTool']);

const handleClose = () => {
  emit('close');
};

const handleToolChange = (tool) => {
  emit('changeTool', tool);
};
</script>

<style scoped>
.bottom-content-container {
}
.tool-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
}

.tool-icon {
  width: 30px;
}

.other-icons {
  display: flex;
  gap: 10px;
}

.content {
  background: rgba(0, 0, 0, 0.5);
}
</style>
