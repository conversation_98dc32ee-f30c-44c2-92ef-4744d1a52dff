<template>
  <van-action-sheet
    v-model:show="localShow"
    :style="{ height: '70vh' }"
    :close-on-click-overlay="true"
    round
    teleport="body"
  >
    <div class="bg-white h-full flex flex-col">
      <!-- Header with Vant Tabs -->
      <div class="flex-none">
        <van-tabs 
          v-model:active="activeTab" 
          :line-width="30"
          :line-height="2"
          :border="false"
          title-active-color="#000000"
          title-inactive-color="#999999"
          background="transparent"
          swipeable
          class="sheet-tabs"
        >
          <van-tab title="在线贵族(2)" name="vip">
            <div class="px-4 py-4">
              <!-- VIP Privilege Banner -->
              <div class="bg-white rounded-full p-2 pl-4 mb-4 shadow-[0px_0px_54px_0px_rgba(166,189,198,0.32)]">
                <div class="flex items-center justify-between">
                  <div class="flex items-center space-x-2">
                    <span class="text-gray-700">加入贵族，享受独一无二的豪华特权</span>
                  </div>
                  <button class="bg-[var(--van-primary-color)] text-white px-6 py-2 rounded-full text-sm font-medium border-none">
                    查看详情
                  </button>
                </div>
              </div>

              <!-- VIP Members List -->
              <div class="space-y-4">
                <div 
                  v-for="(member, index) in vipMembers" 
                  :key="index"
                  class="flex items-center space-x-3 py-2"
                >
                  <!-- Avatar -->
                  <div class="relative">
                    <img 
                      :src="member.avatar" 
                      :alt="member.name"
                      class="w-8 h-8 rounded-full object-cover"
                    />
                  </div>
                  
                  <!-- User Info -->
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <span class="text-gray-900 font-medium">{{ member.name }}</span>
                      <img class="block" width="21" height="21" src="@/assets/live/gender_01.png" fit="cover" />
                      <van-image class="block w-7" :src="getRankIcon(2)" fit="cover" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </van-tab>
          <van-tab title="在线用户(12996)" name="users">
            <div class="px-4 py-4">
              <div class="space-y-4">
                <div 
                  v-for="(member, index) in onlineUsers" 
                  :key="index"
                  class="flex items-center space-x-3 py-2"
                >
                  <!-- Avatar -->
                  <div class="relative">
                    <img 
                      :src="member.avatar" 
                      :alt="member.name"
                      class="w-8 h-8 rounded-full object-cover"
                    />
                  </div>
                  
                  <!-- User Info -->
                  <div class="flex-1">
                    <div class="flex items-center space-x-2">
                      <span class="text-gray-900 font-medium">{{ member.name }}</span>
                      <img class="block" width="21" height="21" src="@/assets/live/gender_01.png" fit="cover" />
                      <van-image class="block w-7" :src="getRankIcon(2)" fit="cover" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </van-tab>
        </van-tabs>
      </div>
    </div>
  </van-action-sheet>
</template>

<script setup>
import { ref, computed } from 'vue';
import { getRankIcon } from '@/utils/vipLv';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:show']);

const localShow = computed({
  get: () => props.show,
  set: (value) => emit('update:show', value)
});

const activeTab = ref('vip');

// Demo VIP members data
const vipMembers = ref([
  {
    name: '权品暴力奶',
    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
    vipLevel: 'LV15'
  },
  {
    name: '小春雨',
    avatar: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=facearea&w=256&h=256&q=80',
    vipLevel: 'LV20'
  }
]);

// Demo online users data
const onlineUsers = ref([
  {
    name: '用户1',
    avatar: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=facearea&w=256&h=256&q=80',
    level: 'LV5'
  },
  {
    name: '用户2',
    avatar: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=facearea&w=256&h=256&q=80',
    level: 'LV8'
  },
  {
    name: '用户3',
    avatar: 'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?auto=format&fit=facearea&w=256&h=256&q=80',
    level: 'LV3'
  },
  {
    name: '用户4',
    avatar: 'https://images.unsplash.com/photo-1519340333755-c1aa5571fd46?auto=format&fit=facearea&w=256&h=256&q=80',
    level: 'LV12'
  },
  {
    name: '用户5',
    avatar: 'https://images.unsplash.com/photo-1519125323398-675f0ddb6308?auto=format&fit=facearea&w=256&h=256&q=80',
    level: 'LV7'
  }
]);
</script>

<style scoped>
.sheet-tabs {
  font-size: 12px;
  font-family: var(--van-base-font);
}
.sheet-tabs .van-tab {
  font-size: 16px;
  font-weight: 500;
  padding: 12px 16px;
}
:deep(.sheet-tabs .van-tabs__line) {
  background: var(--van-primary-color);
}
</style> 