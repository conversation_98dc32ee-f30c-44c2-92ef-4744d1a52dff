<template>
  <div class="new-user-join-container">
    <div class="dropdown-block">
      <div class="background">
        <img src="@/assets/live/bg_new_user_join_example.png" alt="New User" />
      </div>
      <div class="new-user-join-content">
        <!-- User Avatar with Badge -->
        <div class="down-icon-container" @click="handleArrowIconClick">
          <img src="@/assets/live/down_icon.png" alt="User Avatar" :class="{ 'down-icon': true, 'flip-up': showDropdownContent }" />
        </div>
        <BetSignal />
      </div>
    </div>
    <div v-show="showDropdownContent" class="dropdown-content">
      <div class="bet-signal-content">
        <BetSignal />
        <BetSignal />
        <BetSignal />
      </div>
    </div>
  </div>
</template>

<script setup>
import BetSignal from './BetSignal.vue';
const props = defineProps({});

const emit = defineEmits([]);

const showDropdownContent = ref(false);

const handleArrowIconClick = () => {
  console.log('handleArrowIconClick');
  showDropdownContent.value = !showDropdownContent.value;
};

watch(
  () => showDropdownContent.value,
  (val) => {
    console.log('showDropdownContent', val);
  }
);
</script>

<style scoped>
.new-user-join-container {
  display: flex;
  align-items: center;
  margin: 8px 0;
  position: relative;
  pointer-events: auto;
}

.new-user-join-content {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  padding: 4px;
  border-radius: 20px;
  gap: 10px;
}

.down-icon-container {
  position: relative;
  flex-shrink: 0;
  padding-left: 5px;
}

.down-icon {
  max-height: 27px;
  border-radius: 50%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.user-info {
  min-width: 150px;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.flip-up {
  transform: rotate(180deg);
}

.user-details {
  display: flex;
  align-items: center;
  gap: 2px;
}

.username {
  font-size: 10px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
}

.level-badge {
  display: flex;
  align-items: center;
  position: relative;
}

.level-icon {
  width: 32px;
  height: 18px;
  object-fit: contain;
}

.join-message {
  font-size: 12px;
  color: #ffffff;
  opacity: 0.95;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  font-weight: 500;
}

.new-user-icon-container {
  position: relative;
  flex-shrink: 0;
}

.new-user-icon {
  width: auto;
  height: 60px;
  object-fit: contain;
}

.dropdown-content {
  background-color: #0000003c;
  position: absolute;
  bottom: -10px;
  left: 0;
  right: 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  border-radius: 20px;
}

.bet-signal-content {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-left: 45px;
}
</style>
