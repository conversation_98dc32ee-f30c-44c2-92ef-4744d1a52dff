<template>
  <div class="bg-white h-full flex flex-col">
    <!-- Header with <PERSON><PERSON> -->
    <div class="flex justify-between items-center p-4">
      <div class="w-[35px] h-[35px]"></div>
      <div class="text-[#333333] text-[18px] font-bold">守护榜</div>
      <img class="w-[35px] h-[35px]" src="@/assets/live/question_mark_icon.png" />
    </div>

    <GuardianHeader :user="user" />

    <GuardianTable :list="guardianList"/>
     <!-- <GuardianTable :list="[]"/> -->

    <div class="p-4 flex gap-1 items-center justify-between">
      <div class="flex gap-1 items-center">
        <img src="@/assets/live/icon-!.png" class="w-[20px] h-[20px]" />

        <div class="text-[#00ccff] text-[12px] font-bold">守护主播获得尊贵身份、特权</div>
      </div>
      <img src="@/assets/live/enable_guardian_btn.png" class="h-[43px] cursor-pointer" @click="handleUpgrade" />
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { getRankIcon } from '@/utils/vipLv';
import GuardianHeader from './GuardianHeader.vue';
import GuardianTable from './GuardianTable.vue';

const emit = defineEmits(['upgrade']);

const selectedGuardian = ref(0);

const user = ref({
  name: '小纯御',
  avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
  level: 'LV15'
});

const guardianList = ref([
  {
    name: '菜鸡小可乐',
    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
    rankIcon: 'src/assets/live/guardian_icon_1.png',
    guardDay: 200,
    amount: 1200
  },
  {
    name: '不帮抓就摆烂',
    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
    rankIcon: 'src/assets/live/guardian_icon_2.png',
    guardDay: 180,
    amount: 1100
  },
  {
    name: '初夏奈奈',
    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
    rankIcon: 'src/assets/live/guardian_icon_3.png',
    guardDay: 182,
    amount: 620
  },
  {
    name: '不帮抓就摆烂',
    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
    rankIcon: 'src/assets/live/guardian_icon_3.png',
    guardDay: 182,
    amount: 620
  },
  {
    name: '不帮抓就摆烂',
    avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80',
    rankIcon: 'src/assets/live/guardian_icon_3.png',
    guardDay: 182,
    amount: 620
  }
]);

const handleUpgrade = () => {
  emit('upgrade');
};
</script>

<style scoped></style>
