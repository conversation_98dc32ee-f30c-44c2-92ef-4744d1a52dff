<template>
  <SecondPageLayout title="守护详情">
    <div class="min-h-screen flex flex-col overflow-y-auto">
      <!-- Gradient background section -->
      

      <div class="flex-1 bg-[#F5F6FC] px-6 py-8 min-h-[200px]">
        <p class="text-gray-500 mb-4">
          这个人很懒，什么也没留下~
        </p>

        <!-- Contribution List -->
        <div class="w-full max-w-sm mb-8">
          <div class="bg-white px-4 py-2 rounded-full shadow-[0px_0px_54px_0px_rgba(166,189,198,0.32)]">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <!-- Chart icon placeholder -->
                <img class="block" width="15" height="15" src="@/assets/live/<EMAIL>" fit="cover" />
                <span class="text-gray-700 font-medium">个人贡献榜</span>
              </div>

              <!-- Contribution avatars stack -->
              <div class="flex items-center">
                <div v-for="(contributor, index) in contributionList" :key="index" class="relative -ml-2">
                  <img :src="contributor.avatar" :alt="contributor.name"
                    class="w-10 h-10 rounded-full object-cover border-2 border-solid border-[var(--van-primary-color)] shadow-sm" />
                </div>
              </div>

              <img class="block" width="10" height="10" src="@/assets/webp/icon/icon-arrow.webp" fit="contain" />
            </div>
          </div>
        </div>

        <!-- More info here -->

        <!-- Personal Information Section -->
        <div class="mb-8">
          <div class="flex items-center space-x-2 mb-4">
            <img class="block" width="24" height="24" src="@/assets/webp/freeback.webp" fit="cover" />
            <h3 class="font-medium text-gray-800">个人信息</h3>
          </div>

          <div class="bg-white rounded-2xl p-4">
            <div class="grid grid-cols-2 gap-4">
              <!-- Hometown -->
              <div class="flex items-center space-x-3">
                <span class="text-gray-400 text-sm font-medium">家乡</span>
                <span class="text-gray-800 font-medium">{{ personalInfo.hometown }}</span>
              </div>

              <!-- Age -->
              <div class="flex items-center space-x-3">
                <span class="text-gray-400 text-sm font-medium">年龄</span>
                <span class="text-gray-800 font-medium">{{ personalInfo.age }}</span>
              </div>

              <!-- Occupation -->
              <div class="flex items-center space-x-3">
                <span class="text-gray-400 text-sm font-medium">职业</span>
                <span class="text-gray-800 font-medium">{{ personalInfo.occupation }}</span>
              </div>

              <!-- Relationship Status -->
              <div class="flex items-center space-x-3">
                <span class="text-gray-400 text-sm font-medium">情感</span>
                <span class="text-gray-800 font-medium">{{ personalInfo.relationship }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Follow Button -->
        <div class="mt-8">
          <button class="w-full bg-[var(--van-primary-color)] text-white py-2 rounded-full text-lg font-medium border-none">
            关注
          </button>
        </div>
        
        <!-- Test Button for Business Card Popup -->
        <div class="mt-4">
          <button 
            class="w-full bg-gradient-to-r from-pink-400 to-orange-400 text-white py-2 rounded-full text-lg font-medium border-none"
            @click="showBusinessCardPopup = true"
          >
            查看名片
          </button>
        </div>

      </div>
    </div>
  </SecondPageLayout>
  
  <!-- Business Card Popup -->
  <BusinessCardPopup v-model:show="showBusinessCardPopup" />
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { showToast } from 'vant';
import SecondPageLayout from '@/layouts/SecondPageLayout.vue';
import useClipboard from 'vue-clipboard3';
import BusinessCardPopup from './components/BusinessCardPopup.vue';

const { toClipboard } = useClipboard();

const route = useRoute();

// Idol data
const idolData = ref({
  name: '不帮抓就摆烂',
  memberId: '676564868',
  followers: '9999',
  following: '200',
  avatar: 'https://images.unsplash.com/photo-1517841905240-472988babdf9?auto=format&fit=facearea&w=256&h=256&q=80'
});

// Contribution list data
const contributionList = ref([
  {
    name: '贡献者1',
    avatar: 'https://images.unsplash.com/photo-1508214751196-bcfd4ca60f91?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '贡献者2',
    avatar: 'https://images.unsplash.com/photo-1465101046530-73398c7f28ca?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '贡献者3',
    avatar: 'https://images.unsplash.com/photo-1515378791036-0648a3ef77b2?auto=format&fit=facearea&w=256&h=256&q=80'
  },
  {
    name: '贡献者4',
    avatar: 'https://images.unsplash.com/photo-1503023345310-bd7c1de61c7d?auto=format&fit=facearea&w=256&h=256&q=80'
  }
]);

const personalInfo = ref({
  hometown: '中国北京',
  age: '26',
  occupation: '电竞职业选手',
  relationship: '单身，可追'
});

const copyMemberId = () => {
  toClipboard(idolData.value.memberId);
  showToast('复制成功');
};

// Business Card Popup state
const showBusinessCardPopup = ref(false);

// Load idol data based on route params
onMounted(() => {
  const hostId = route.params.id;
  // TODO: Fetch idol data from API using hostId
  console.log('Loading idol data for ID:', hostId);
});
</script>

<style scoped>
/* Custom gradient background */
.bg-gradient-to-br {
  background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 50%, #06b6d4 100%);
}

/* Backdrop blur effect */
.backdrop-blur-sm {
  backdrop-filter: blur(4px);
}

/* Ensure full height */
.h-full {
  height: 100vh;
}
</style>
