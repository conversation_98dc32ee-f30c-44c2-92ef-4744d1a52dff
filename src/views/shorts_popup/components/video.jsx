import { uActions } from '@/api/user';
import { key, iv } from '@/constants/img';
import { generatepsign } from '@/utils/psign';
import { getImageBase64 } from '@/utils/decrypto';
import { durationSeconds } from '@/utils';

export default defineComponent({
  name: 'VideoComponents',
  props: {
    vid: Number,
    isCloudMode: Boolean,
    cloudFileId: [Number, String],
    playUrl: String,
    cloudUrl: String,
    videoCover: String,
    videoDuration: Number,
    playCall: Function,
    timeupdateCall: Function,
    similarityType: Number,
    durationType: Number
  },
  setup(props, { emit }) {
    let player = null;
    const videoRef = ref(null);
    const playerRef = ref(null);
    const isAction = ref(false);
    const current = ref(0);
    const duration = ref(0);
    const percentage = ref(0);
    const [play, toggle] = useToggle(false);

    const renderIcon = () => {
      if (!play.value) {
        return (
          <div className="w-20 h-20">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              height="24"
              viewBox="0 -960 960 960"
              width="24"
              focusable="false"
              style="pointer-events: none; display: block; width: 100%; height: 100%;">
              <path
                d="M320-273v-414q0-17 12-28.5t28-11.5q5 0 10.5 1.5T381-721l326 207q9 6 13.5 15t4.5 19q0 10-4.5 19T707-446L381-239q-5 3-10.5 4.5T360-233q-16 0-28-11.5T320-273Z"
                fill="white"
              />
            </svg>
          </div>
        );
      }
    };

    const renderProgress = () => {
      return (
        <div className="absolute right-4 left-4 bottom-2 h-10 flex items-center justify-between">
          <span className="mr-2 text-xs">{durationSeconds(current.value, true)}</span>
          <van-progress show-pivot={false} percentage={percentage.value} class="w-full"></van-progress>
          <span className="ml-2 text-xs">{durationSeconds(duration.value, true)}</span>
        </div>
      );
    };

    const progressHandle = (paylod) => {
      current.value = paylod.current;
      duration.value = paylod.duration;
      percentage.value = Math.min((paylod.current / paylod.duration) * 100, 100);
    };

    const playHandle = () => {
      if (play.value === true) {
        player?.pause();
      } else {

        player?.play();

        // const allow = props.playCall?.();
        // if (allow) {
        //   player?.play();
        // }
      }
    };

    watch(
      () => props.vid,
      async (next, prev, onCleanup) => {
        try {
          const opts = {
            licenseUrl: import.meta.env.VITE_GLOB_APP_PLAY_LICENSE_URL,
            fakeFullscreen: true,
            bigPlayButton: false,
            autoplay: true,
            controls: false
          };
      
          if (props.isCloudMode) {
            opts.fileID = props.cloudFileId;
            opts.appID = import.meta.env.VITE_APP_PLAY_APP_ID;
            opts.psign = generatepsign(props.cloudFileId, props.playUrl);
          } else {
            opts.sources = [{ src: props.cloudUrl }];
          }

          await nextTick();

          player = window.TCPlayer(unref(videoRef), opts);

          player.on('loadedmetadata', () => {
            duration.value = player.duration();
          });

          player.on('loadeddata', () => {
            console.log('loadeddata');
            player.play();
          });

          player.on('timeupdate', () => {
            const currentTime = player.currentTime();
            current.value = currentTime;
            percentage.value = Math.min((currentTime / duration.value) * 100, 100);
            props.timeupdateCall(currentTime);
          });

          player.on('play', () => {
            const allow = props.playCall?.();
            if (!allow) {
              player?.pause();
            } else {
              play.value = true;
              if (isAction.value === false) {
                isAction.value = true;
                emit('left');
              }
            }
          });

          player.on('pause', () => {
            play.value = false;
          });

          getImageBase64(props.videoCover, key, iv).then((res) => {
            player.poster(res);
          });

          onCleanup(() => {
            if (player.currentTime() > 0) {
              uActions({ actionType: 4, eventId: props.vid, watchingTime: Math.floor(player.currentTime()) });
            };
            if (player) {
              player.dispose();
              player = null;
            }
          })

        } catch (error) {
          console.log(error);
        }
      },
      { immediate: true }
    );

    return () => {
      return (
        <div className="w-full h-full">
          <div className="w-full h-full relative overflow-hidden" onClick={playHandle}>
            <video
              ref={videoRef}
              id={`short_id_${props.vid}`}
              className="absolute top-0 left-0 w-full h-full outline-none object-none pointer-events-none"
              preload="auto"
              x5-playsinline="true"
              playsinline
              webkit-playsinline />
            <div className="player-start absolute top-50% left-50% z-[5] cursor-pointer" style="transform: translate(-50%,-50%)">
              {renderIcon()}
            </div>
            {renderProgress()}
          </div>
        </div>
      );
    };
  }
});
