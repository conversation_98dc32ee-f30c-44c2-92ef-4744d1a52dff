<template>
  <SecondPageLayout>
    <!-- <div class="login_logo mx-auto w-[196px] h-[55.75px] mt-12">
      <img src="/static/image/login-logo.png" alt="">
    </div> -->

    <div className="login_logo mx-auto w-[196px] h-[55.75px] mt-12" v-if="merchantLogo">
      <ImgComponents :imgUrl="merchantLogo?.bind_phone_logo" />
    </div>

    <CountrySelector v-model="selectedCountry" />

    <div class="login_form w-11/12 mx-auto">
      <van-form ref="bindForm" :show-error-message="false">
        <div class="flex items-stretch h-[43px] mb-6 rounded-full bg-[var(--van-black-500)] overflow-hidden">
          <div class="flex items-center justify-center w-[90px] text-[13px] text-[var(--van-primary-color)]">
            <span class="font-bold">+{{ selectedCountry.code }}</span>
          </div>
          <div class="flex-1">
            <van-field
              v-model="params.tel"
              name="tel"
              type="tel"
              autocomplete="off"
              placeholder="请输入手机号码"
              label-align="top"
              :rules="[{ required: true, message: '请输入手机号码' }]"
            >
            </van-field>
          </div>
          <div
            class="flex items-center justify-center w-[100px] text-[13px] rounded-r-full text-black bg-[var(--van-primary-color)]"
            @click.stop="getCaption"
          >
            <span v-if="isActive">还剩{{ t }}秒</span>
            <span v-else>获取验证码</span>
          </div>
        </div>
        <div class="flex items-stretch h-[43px] mb-6 rounded-full bg-[var(--van-black-500)] overflow-hidden">
          <div class="flex items-center justify-center w-[90px] text-[13px] text-[var(--van-primary-color)]">
            <span>验证码</span>
          </div>
          <div class="flex-1">
            <van-field
              v-model="params.code"
              name="code"
              autocomplete="off"
              placeholder="请输入验证码"
              label-align="top"
              :rules="[{ required: true, message: '请输入验证码' }]"
            >
            </van-field>
          </div>
        </div>
      </van-form>
      <div class="login_btn mt-12">
        <van-button type="primary" block round @click="onSubmit">绑 定</van-button>
      </div>
    </div>
  </SecondPageLayout>
</template>

<script setup>
import { useAppStore } from '@/store/app';
import { useUserStore } from '@/store/user';
import { uGetTelCode, uBindingTel } from '@/api/user';
import { useLogin, useCaption } from '@/hooks';
import CountrySelector from '@/components/CountrySelector/index.vue';

const appStore = useAppStore();
const merchantLogo = computed(() => appStore.appConfig?.merchantLogo);

const router = useRouter();
const userStore = useUserStore();

const bindForm = ref(null);

const params = reactive({
  tel: '',
  code: ''
});

const selectedCountry = ref({ name: '中国', code: 86 });

const { t, isActive, isPending, getCaption } = useCaption(bindForm, () => ({ tel: params.tel, pre: `+${selectedCountry.value.code}`, codeType: 'Bind' }));

const onFailed = (errorInfo) => {
  showToast(errorInfo.errors[0].message);
};

const onSubmit = () => {
  bindForm.value.validate('tel').then(() => {
    if (isPending.value === false) {
      showToast('请先发送验证码');
      return;
    }
    bindForm.value.validate('code').then(async () => {
      try {
        const res = await uBindingTel(params);
        if (res) {
          await userStore.updatePersonData();
          setTimeout(() => {
            router.replace({ path: '/bind_phone_success' });
          }, 500);
        }
      } catch (error) {

      }
    }).catch((err) => {
      showToast(err.message);
    });
  }).catch((err) => {
    showToast(err.message);
  });
};
</script>
