import { createServiceExtra } from "@/utils/useAxiosApi";

const useAxiosApi = createServiceExtra('url_promotion');


/**
 * 签名获取
 * @returns UseAxiosReturn
 */
export const GeneratorSign = (params) => {
  return useAxiosApi('/fetchSign', { params, method: 'GET' })
}

/**
 * 统计数据
 * @returns UseAxiosReturn
 */
export const listReport = (params) => {
  return useAxiosApi('/promotion/rebate/report', { params, method: 'GET' })
}

/**
 * 成员管理
 * @returns UseAxiosReturn
 */
export const listTeamUser = (params) => {
  return useAxiosApi('/promotion/team_user/team', { params, method: 'GET' })
}

/**
 * 成员管理--统计数据
 * @returns UseAxiosReturn
 */
export const listTeamUserReport = (params) => {
  return useAxiosApi('/promotion/team_user/team_user_report', { params, method: 'GET' })
}

/**
 * 成员详情
 * @returns UseAxiosReturn
 */
export const listTeamUseInfo = (params) => {
  return useAxiosApi('/promotion/team_user/team_user_info', { params, method: 'GET' })
}

/**
 * 成员管理--保存修改
 * @returns UseAxiosReturn
 */
export const saveTeamUser = (data) => {
  return useAxiosApi('/promotion/team_user/save_team_user_divide', { data, method: 'POST' })
}

/**
 * 我要赚钱
 * @returns UseAxiosReturn
 */
export const listSource = (params) => {
  return useAxiosApi('/promotion/plan/source', { params, method: 'GET' })
}

/**
 * 上下级
 * @returns UseAxiosReturn
 */
export const listCenter = (params) => {
  return useAxiosApi('/promotion/plan/center', { params, method: 'GET' })
}

/**
 * 保存分成比例
 * @returns UseAxiosReturn
 */
export const saveDivide = (data) => {
  return useAxiosApi('/promotion/plan/save_default_divide', { data, method: 'POST' })
}

/**
 * 保存名片
 * @returns UseAxiosReturn
 */
export const saveInfo = (data) => {
  return useAxiosApi('/promotion/plan/save_info', { data, method: 'POST' })
}

/**
 * 游戏记录
 * @returns UseAxiosReturn
 */
export const listGameRecord = (params) => {
  return useAxiosApi('/promotion/game/game_record', { params, method: 'GET' })
}

/**
 * 游戏详情
 * @returns UseAxiosReturn
 */
export const listGameDetail = (params) => {
  return useAxiosApi('/promotion/game/game_detail', { params, method: 'GET' })
}

/**
 * 绑定下级
 * @returns UseAxiosReturn
 */
export const bindUser = (data) => {
  return useAxiosApi('/promotion/team_user/bind_user', { data, method: 'POST' })
}

/**
 * 佣金方案平台
 * @returns UseAxiosReturn
 */
export const listDividePlatform = (params) => {
  return useAxiosApi('/promotion/plan/divide_platform', { params, method: 'GET' })
}

/**
 * 佣金方案
 * @returns UseAxiosReturn
 */
export const listDivide = (params) => {
  return useAxiosApi('/promotion/plan/divide_app', { params, method: 'GET' })
}

/**
 * 下级佣金方案
 * @returns UseAxiosReturn
 */
export const listDivideSub = (params) => {
  return useAxiosApi('/promotion/plan/default_divide', { params, method: 'GET' })
}

/**
 * 佣金概览
 * @returns UseAxiosReturn
 */
export const listRebateReport = (params) => {
  return useAxiosApi('/promotion/rebate/rebate_report', { params, method: 'GET' })
}

/**
 * 佣金概览
 * @returns UseAxiosReturn
 */
export const listRebateCash = (params) => {
  return useAxiosApi('/promotion/rebate/rebate_cash', { params, method: 'GET' })
}

/**
 * 专属返水，可领取的汇总数据
 * @returns UseAxiosReturn
 */
export const TotalClaimInfoAPI = (data = {}) => {
  return useAxiosApi('/promotion/rebateExclusive/totalClaimInfo', { data, method: 'POST' })
}

/**
 * 专属返水，某段时间内的领取情况
 * @returns UseAxiosReturn
 */
export const TimeRegionClaimInfoAPI = (data = {}) => {
  return useAxiosApi('/promotion/rebateExclusive/timeRegionClaimInfo', { data, method: 'POST' })
}

/**
 * 领取某一天剩余的返水
 * @returns UseAxiosReturn
 */
export const ClaimOneDayAPI = (data = {}) => {
  return useAxiosApi('/promotion/rebateExclusive/claimOneDay', { data, method: 'POST' })
}

/**
 * 一键领取所有的
 * @returns UseAxiosReturn
 */
export const ClaimAllAPI = (data = {}) => {
  return useAxiosApi('/promotion/rebateExclusive/claimAll', { data, method: 'POST' })
}
/**
 * 返水规则
 * @returns UseAxiosReturn
 */
export const GetRebateRateAPI = (data = {}) => {
  return useAxiosApi('/promotion/rebateExclusive/getRebateRate', { data, method: 'POST' })
}