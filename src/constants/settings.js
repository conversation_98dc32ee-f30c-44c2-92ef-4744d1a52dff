import {
  iconSettingGift,
  iconSettingGiftOff,
  iconSettingVehicle,
  iconSettingVehicleOff,
  iconSettingSound,
  iconSettingSoundOff,
  iconSettingVibration,
  iconSettingVibrationOff,
  iconSettingChat,
  iconSettingChatOff,
  iconSettingStealth,
  iconSettingStealthOff,
  iconSettingInvisible,
  iconSettingInvisibleOff,
  iconSettingReminder,
  iconSettingReminderOff,
  iconSettingSpecial,
  iconSettingSpecialOff
} from '@/assets/live/icons.js'

export const SETTINGS_UI_CONFIG = {
  gift: {
    key: 'gift',
    label: '礼物特效',
    description: '显示礼物特效动画',
    activeIcon: iconSettingGift,
    inactiveIcon: iconSettingGiftOff
  },
  seat: {
    key: 'seat',
    label: '座驾特效',
    description: '显示座驾特效动画',
    activeIcon: iconSettingVehicle,
    inactiveIcon: iconSettingVehicleOff
  },
  sound: {
    key: 'sound',
    label: '中奖提示音',
    description: '中奖时播放提示音',
    activeIcon: iconSettingSound,
    inactiveIcon: iconSettingSoundOff
  },
  vibration: {
    key: 'vibration',
    label: '中奖震动',
    description: '中奖时设备震动',
    activeIcon: iconSettingVibration,
    inactiveIcon: iconSettingVibrationOff
  },
  message: {
    key: 'message',
    label: '游戏消息',
    description: '显示游戏相关消息',
    activeIcon: iconSettingChat,
    inactiveIcon: iconSettingChatOff
  },
  stealth: {
    key: 'stealth',
    label: '入场隐身',
    description: '进入房间时隐身',
    activeIcon: iconSettingStealth,
    inactiveIcon: iconSettingStealthOff
  },
  invisible: {
    key: 'invisible',
    label: '榜单隐身',
    description: '在排行榜中隐身',
    activeIcon: iconSettingInvisible,
    inactiveIcon: iconSettingInvisibleOff
  },
  reminder: {
    key: 'reminder',
    label: '长龙提醒',
    description: '长龙出现时提醒',
    activeIcon: iconSettingReminder,
    inactiveIcon: iconSettingReminderOff
  },
  special: {
    key: 'special',
    label: '长龙特效',
    description: '显示长龙特效动画',
    activeIcon: iconSettingSpecial,
    inactiveIcon: iconSettingSpecialOff
  }
};

// Settings keys for easy iteration
export const SETTINGS_KEYS = Object.keys(SETTINGS_UI_CONFIG);

// Default settings values
export const DEFAULT_SETTINGS = {
  gift: true,
  seat: false,
  sound: true,
  vibration: false,
  message: true,
  stealth: false,
  invisible: true,
  reminder: false,
  special: true
};

// Chat settings defaults
export const DEFAULT_CHAT_SETTINGS = {
  backgroundOpacity: 50,
  fontSize: 20
};

// Helper function to get setting UI config
export const getSettingUIConfig = (key) => SETTINGS_UI_CONFIG[key];

// Helper function to get all settings with UI config
export const getAllSettingsWithUIConfig = (settingsState) => {
  return SETTINGS_KEYS.map(key => ({
    ...SETTINGS_UI_CONFIG[key],
    isActive: settingsState[key] || false
  }));
}; 