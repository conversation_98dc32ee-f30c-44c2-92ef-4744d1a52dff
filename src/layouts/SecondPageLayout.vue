<template>
    <div class="flex flex-col h-full relative">
      <div role="main" class="flex flex-col h-full relative overflow-hidden z-[1]">
        <slot name="navbar">
          <van-nav-bar safe-area-inset-top left-arrow @click-left="onClickLeft" @click-right="onClickRight" :border="false" z-index="1000" :fixed="$route.meta.fixedNavbar" :style="{ backgroundColor: $route.meta.navbarBgColor }">
            <template #title>{{ title || $route.meta.title }}</template>
            <template #right>
              <slot name="navbar-right"></slot>
            </template>
          </van-nav-bar>
        </slot>

        <slot name="nav-extra"></slot>

        <div class="flex flex-col flex-1 h-full overflow-y-scroll relative">
          <slot></slot>
        </div>
      </div>
    </div>
</template>

<script setup>
defineOptions({
  name: 'SecondPageLayout'
});

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  clickLeftHandle: Function
});

const emits = defineEmits(['click-right']);

const onClickLeft = () => {
  if (props.clickLeftHandle) {
    props.clickLeftHandle();
  } else {
    history.back();
  }
};

const onClickRight = () => {
  emits('click-right');
};
</script>
