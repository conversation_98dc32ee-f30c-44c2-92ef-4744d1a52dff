# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Vue 3 + Vite livestream player application built as a mobile-first Progressive Web App (PWA). The application features video streaming, gaming functionality, user authentication, and payment systems.

## Development Commands

**Development Server**
```bash
npm run dev                    # Start development server on port 7779
npm run preview               # Preview production build
```

**Build Commands**
```bash
npm run develop              # Build for development environment
npm run release              # Build for production/release
npm run run-build           # Production build with PWA debug info
npm run run-build-report    # Build with bundle analyzer report
```

**Service Worker Builds**
```bash
npm run run-build-sw        # Build with service worker
npm run run-build-claims    # Build with claims-based service worker
```

**Linting**
```bash
npx eslint .                # Run ESLint (uses @antfu/eslint-config)
```

## Architecture

### Frontend Stack
- **Vue 3** with Composition API and `<script setup>`
- **Vite** for build tooling and development
- **Vue Router** for client-side routing
- **Pinia** for state management with persistence
- **Vant 4** as the mobile UI component library
- **UnoCSS** for utility-first CSS
- **VueQuery** for server state management

### Key Technologies
- **PWA** with service worker and offline capabilities
- **WebSocket** connection for real-time features
- **Fingerprint.js** for device identification
- **Swiper** for carousel/slider components
- **ECharts** for data visualization
- **Crypto-js** for encryption/decryption

### Project Structure
```
src/
├── api/           # API service modules
├── assets/        # Static assets (images, icons, etc.)
├── components/    # Reusable Vue components
├── composables/   # Vue composition functions
├── hooks/         # Custom React-style hooks
├── layouts/       # Page layout components
├── plugins/       # Vue plugins and initializers
├── router/        # Vue Router configuration
├── sdk/           # Custom SDK and WebSocket handling
├── store/         # Pinia store modules
├── styles/        # Global styles and Less files
├── utils/         # Utility functions
└── views/         # Page-level components
```

### State Management
- **Pinia stores**: app, game, user, video, route
- **Persistent state** using pinia-plugin-persistedstate
- **VueQuery** for server state with 5-minute stale time

### Build Configuration
- **Mobile-first** with postcss-mobile-forever (375px viewport)
- **Terser** minification with console/debugger removal
- **Manual chunking** for node_modules optimization
- **Source maps** controlled by SOURCE_MAP env variable

### Component Architecture
- **Composition API** with `<script setup>`
- **Auto-imports** for Vue APIs and components
- **JSX support** for complex render logic
- **CSS Modules** with camelCase conversion
- **Vant component** auto-import resolution

## Development Notes

### Environment Setup
- Development server runs on port 7779
- Uses `.env` files for environment configuration
- Requires `VITE_TOKEN_KEY` for authentication

### Mobile Development
- Configured for mobile viewport (375px base)
- Max display width of 600px
- Disabled landscape orientation
- Touch-friendly interactions

### WebSocket Integration
- Custom SDK in `src/sdk/` handles WebSocket connections
- Encryption/decryption support for messages
- Token-based authentication

### Authentication Flow
- Multi-type login (account, phone, guest)
- Device fingerprinting for identification
- Token-based session management
- Login state persistence

### Gaming Features
- Lottery/gambling game components
- Chip betting system
- Real-time game state updates
- Result visualization

## Key Files
- `src/App.vue` - Main application component with global state
- `src/main.js` - Application bootstrap and plugin setup
- `vite.config.ts` - Build configuration
- `src/router/index.js` - Route configuration
- `src/store/index.js` - Pinia store setup
- `src/sdk/index.js` - WebSocket SDK initialization